package by.algin.exception;

import by.algin.constants.CommonMessageConstants;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Common Feign error handler used across all services
 * Provides consistent error handling for Feign client calls
 */
@Slf4j
@Component
public class FeignErrorHandler {

    /**
     * Handle Feign exception and extract user-friendly message
     * @param ex Feign exception
     * @param operation Operation being performed
     * @param context Additional context (e.g., username, project ID)
     * @return User-friendly error message
     */
    public String handleFeignError(FeignException ex, String operation, String context) {
        String userMessage = extractUserFriendlyMessage(ex);
        logFeignError(ex, operation, context);
        return userMessage;
    }

    /**
     * Handle authentication-specific Feign errors
     * @param ex Feign exception
     * @param operation Operation being performed
     * @param username Username context
     * @return User-friendly error message
     */
    public String handleAuthFeignError(FeignException ex, String operation, String username) {
        String userMessage = extractAuthUserFriendlyMessage(ex);
        logFeignError(ex, operation, username);
        return userMessage;
    }

    /**
     * Handle Feign error with Model (for WebUI compatibility)
     * @param ex Feign exception
     * @param model Spring Model
     * @param operation Operation being performed
     * @param username Username context
     */
    public void handleFeignError(FeignException ex, org.springframework.ui.Model model, String operation, String username) {
        String userMessage = extractUserFriendlyMessage(ex);
        if (model != null) {
            model.addAttribute("error", userMessage);
        }
        logFeignError(ex, operation, username);
    }

    /**
     * Handle authentication Feign error with Model (for WebUI compatibility)
     * @param ex Feign exception
     * @param model Spring Model
     * @param operation Operation being performed
     * @param username Username context
     */
    public void handleAuthFeignError(FeignException ex, org.springframework.ui.Model model, String operation, String username) {
        String userMessage = extractAuthUserFriendlyMessage(ex);
        if (model != null) {
            model.addAttribute("error", userMessage);
        }
        logFeignError(ex, operation, username);
    }

    /**
     * Handle service error with Model (for WebUI compatibility)
     * @param ex Exception
     * @param model Spring Model
     * @param operation Operation being performed
     * @param username Username context
     */
    public void handleServiceError(Exception ex, org.springframework.ui.Model model, String operation, String username) {
        if (model != null) {
            model.addAttribute("error", CommonMessageConstants.SERVICE_UNAVAILABLE);
        }
        log.error("Error during {}: for user: {}, error: {}", operation, username, ex.getMessage(), ex);
    }

    /**
     * Extract user-friendly message from Feign exception (public for WebUI compatibility)
     * @param ex Feign exception
     * @return User-friendly message
     */
    public String extractUserFriendlyMessage(FeignException ex) {
        switch (ex.status()) {
            case 400:
                return CommonMessageConstants.INVALID_INPUT;
            case 401:
                return CommonMessageConstants.AUTHENTICATION_REQUIRED;
            case 403:
                return CommonMessageConstants.ACCESS_DENIED;
            case 404:
                return CommonMessageConstants.RESOURCE_NOT_FOUND;
            case 409:
                return CommonMessageConstants.CONFLICT;
            case 500:
                return CommonMessageConstants.INTERNAL_SERVER_ERROR;
            case 503:
                return CommonMessageConstants.SERVICE_UNAVAILABLE;
            default:
                return CommonMessageConstants.EXTERNAL_SERVICE_ERROR;
        }
    }

    /**
     * Extract authentication-specific user-friendly message
     * @param ex Feign exception
     * @return User-friendly message
     */
    private String extractAuthUserFriendlyMessage(FeignException ex) {
        switch (ex.status()) {
            case 400:
                return CommonMessageConstants.INVALID_CREDENTIALS;
            case 401:
                return CommonMessageConstants.AUTHENTICATION_FAILED;
            case 403:
                return CommonMessageConstants.ACCESS_DENIED;
            case 404:
                return CommonMessageConstants.USER_NOT_FOUND;
            case 409:
                if (ex.contentUTF8().contains("email")) {
                    return CommonMessageConstants.EMAIL_ALREADY_EXISTS;
                } else if (ex.contentUTF8().contains("username")) {
                    return CommonMessageConstants.USERNAME_ALREADY_EXISTS;
                }
                return CommonMessageConstants.CONFLICT;
            case 503:
                return CommonMessageConstants.AUTH_SERVICE_UNAVAILABLE;
            default:
                return CommonMessageConstants.AUTHENTICATION_FAILED;
        }
    }

    /**
     * Log Feign error with context
     * @param ex Feign exception
     * @param operation Operation being performed
     * @param context Additional context
     */
    private void logFeignError(FeignException ex, String operation, String context) {
        log.error("Feign error during {}: for context: {}, status: {}, message: {}", 
                 operation, context, ex.status(), ex.getMessage());
        
        if (log.isDebugEnabled()) {
            log.debug("Feign error details: {}", ex.contentUTF8());
        }
    }

    /**
     * Check if error is retryable
     * @param ex Feign exception
     * @return true if retryable, false otherwise
     */
    public boolean isRetryableError(FeignException ex) {
        return ex.status() >= 500 || ex.status() == 408 || ex.status() == 429;
    }

    /**
     * Check if error is authentication related
     * @param ex Feign exception
     * @return true if auth error, false otherwise
     */
    public boolean isAuthError(FeignException ex) {
        return ex.status() == 401 || ex.status() == 403;
    }
}
