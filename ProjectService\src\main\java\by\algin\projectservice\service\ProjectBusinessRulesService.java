package by.algin.projectservice.service;

import by.algin.dto.project.ProjectStatus;
import by.algin.projectservice.constants.ProjectMessageConstants;
import by.algin.projectservice.exception.BusinessRuleViolationException;
import by.algin.projectservice.entity.Project;
import by.algin.projectservice.entity.ProjectMember;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ProjectBusinessRulesService {

    private final ProjectValidationService validationService;
    private final ProjectRoleService roleService;

    public void validateProjectCreation(String projectName, String description, Long ownerId, int userProjectCount) {
        validationService.validateProjectName(projectName);
        validationService.validateProjectDescription(description);
        validationService.validateUserProjectLimit(userProjectCount, ownerId);
    }

    public void validateProjectUpdate(Project project, String newName, String newDescription, Long userId) {
        if (newName != null) {
            validationService.validateProjectName(newName);
        }
        if (newDescription != null) {
            validationService.validateProjectDescription(newDescription);
        }
    }

    public void validateMemberAddition(Project project, Long newMemberId, by.algin.projectservice.entity.ProjectRole role) {
        validationService.validateProjectMemberLimit(project.getMembers().size());
        validationService.validateMemberRole(role);
        validateProjectStatus(project.getStatus());
    }

    public void validateRoleChange(ProjectMember member, by.algin.projectservice.entity.ProjectRole newRole, Long requesterId) {
        validationService.validateMemberRole(newRole);

        if (roleService.isOwnerRole(member.getRole().getRoleName())) {
            throw new BusinessRuleViolationException(ProjectMessageConstants.CANNOT_CHANGE_ROLE_OF_PROJECT_OWNER);
        }

        if (roleService.isOwnerRole(newRole.getRoleName())) {
            throw new BusinessRuleViolationException(ProjectMessageConstants.CANNOT_PROMOTE_MEMBER_TO_OWNER_ROLE);
        }
    }

    public void validateProjectDeletion(Project project, Long userId) {
        if (!project.getOwnerId().equals(userId)) {
            throw new BusinessRuleViolationException(ProjectMessageConstants.ONLY_PROJECT_OWNER_CAN_DELETE_PROJECT);
        }
    }

    public void validateMemberRemoval(ProjectMember member, Long requesterId) {
        if (roleService.isOwnerRole(member.getRole().getRoleName())) {
            throw new BusinessRuleViolationException(ProjectMessageConstants.CANNOT_REMOVE_PROJECT_OWNER_FROM_PROJECT);
        }
    }

    public boolean canManageMembers(ProjectMember member) {
        String roleName = member.getRole().getRoleName();
        return roleService.isManagementRole(roleName);
    }

    public boolean canModifyProject(ProjectMember member) {
        String roleName = member.getRole().getRoleName();
        return roleService.isManagementRole(roleName);
    }

    public boolean canDeleteProject(ProjectMember member) {
        return roleService.isOwnerRole(member.getRole().getRoleName());
    }

    public void validateProjectCanBeModified(Project project) {
        if (project.getStatus() == ProjectStatus.ARCHIVED) {
            throw new BusinessRuleViolationException(ProjectMessageConstants.CANNOT_MODIFY_ARCHIVED_PROJECT);
        }
        if (project.getStatus() == ProjectStatus.COMPLETED) {
            throw new BusinessRuleViolationException(ProjectMessageConstants.CANNOT_MODIFY_COMPLETED_PROJECT);
        }
    }

    public void validateMemberRoleChange(Project project, ProjectMember member, by.algin.projectservice.entity.ProjectRole newRole, Long requesterId) {
        validateRoleChange(member, newRole, requesterId);
    }



    private void validateProjectStatus(ProjectStatus status) {
        if (status == ProjectStatus.ARCHIVED) {
            throw new BusinessRuleViolationException(ProjectMessageConstants.CANNOT_ADD_MEMBERS_TO_ARCHIVED_PROJECT);
        }
        if (status == ProjectStatus.COMPLETED) {
            throw new BusinessRuleViolationException(ProjectMessageConstants.CANNOT_ADD_MEMBERS_TO_COMPLETED_PROJECT);
        }
        if (status == ProjectStatus.INACTIVE) {
            throw new BusinessRuleViolationException(ProjectMessageConstants.CANNOT_ADD_MEMBERS_TO_INACTIVE_PROJECT);
        }
    }
}
