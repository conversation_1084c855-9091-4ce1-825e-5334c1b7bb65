.dashboard-projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(var(--grid-min-width-wide), 1fr));
    gap: var(--spacing-lg);
}

.dashboard-project-card {
    border: 1px solid var(--border-color);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-lg);
    background-color: var(--dashboard-card-bg);
}

.dashboard-project-title {
    margin-top: 0;
    color: var(--text-primary);
}

.dashboard-project-description {
    color: var(--text-light);
    margin: var(--spacing-sm) 0;
}

.dashboard-project-status {
    margin: var(--spacing-sm) 0;
}

.dashboard-project-actions {
    margin-top: var(--spacing-md);
}

.dashboard-project-actions a {
    margin-right: var(--spacing-sm);
    margin-bottom: var(--spacing-xs);
}
