package by.algin.projectservice.config;

import by.algin.constants.CommonProjectConstants;
import by.algin.projectservice.entity.ProjectRole;
import by.algin.projectservice.repository.ProjectRoleRepository;
import by.algin.projectservice.service.RoleConfigurationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
public class RoleInitializer {

    private final ProjectRoleRepository roleRepository;
    private final RoleConfigurationService roleConfigurationService;

    public void initializeRoles() {
        log.info("Initializing project roles");

        if (!roleConfigurationService.getAvailableRoles().isEmpty()) {
            log.info("Configuration-based roles detected, skipping legacy initialization");
            return;
        }

        log.info("No configuration-based roles found, using legacy initialization");
        createOwnerRole();
        createManagerRole();
        createDeveloperRole();
        createViewerRole();

        log.info("Project roles initialization completed");
    }

    private void createOwnerRole() {
        createRoleIfNotExists(
            CommonProjectConstants.ROLE_OWNER,
            "Project Owner",
            "Full control over the project including deletion",
            10,
            "#FF6B35",
            "crown"
        );
    }

    private void createManagerRole() {
        createRoleIfNotExists(
            CommonProjectConstants.ROLE_MANAGER,
            "Project Manager", 
            "Can manage project settings and members",
            7,
            "#4ECDC4",
            "gear"
        );
    }

    private void createDeveloperRole() {
        createRoleIfNotExists(
            CommonProjectConstants.ROLE_DEVELOPER,
            "Developer",
            "Can participate in project development",
            5,
            "#45B7D1",
            "code"
        );
    }

    private void createViewerRole() {
        createRoleIfNotExists(
            CommonProjectConstants.ROLE_VIEWER,
            "Viewer",
            "Can only view project information",
            1,
            "#96CEB4",
            "eye"
        );
    }

    private void createRoleIfNotExists(String roleName, String displayName, String description, 
                                     int permissionLevel, String colorCode, String iconName) {
        Optional<ProjectRole> existingRole = roleRepository.findByRoleNameIgnoreCase(roleName);
        if (existingRole.isEmpty()) {
            ProjectRole role = ProjectRole.builder()
                .roleName(roleName)
                .displayName(displayName)
                .description(description)
                .permissionLevel(permissionLevel)
                .isActive(true)
                .canBeAssigned(true)
                .isSystemRole(true)
                .colorCode(colorCode)
                .iconName(iconName)
                .createdBy("SYSTEM")
                .build();
            
            roleRepository.save(role);
            log.info("Created role: {}", roleName);
        } else {
            log.debug("Role {} already exists, skipping creation", roleName);
        }
    }
}
