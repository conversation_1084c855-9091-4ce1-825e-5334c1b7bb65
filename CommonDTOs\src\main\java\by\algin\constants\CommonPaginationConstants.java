package by.algin.constants;

public final class CommonPaginationConstants {

    // Default pagination values
    public static final int DEFAULT_PAGE = 0;
    public static final int DEFAULT_PAGE_SIZE = 20;
    public static final int MAX_PAGE_SIZE = 100;
    public static final String DEFAULT_SORT_BY = "createdAt";
    public static final String DEFAULT_SORT_DIRECTION = "desc";

    // Members pagination defaults
    public static final String DEFAULT_MEMBERS_SORT_BY = "joinedAt";
    public static final String DEFAULT_MEMBERS_SORT_DIRECTION = "asc";

    // Sort directions
    public static final String SORT_ASC = "asc";
    public static final String SORT_DESC = "desc";

    // Common sortable fields
    public static final String SORT_BY_CREATED_AT = "createdAt";
    public static final String SORT_BY_UPDATED_AT = "updatedAt";
    public static final String SORT_BY_NAME = "name";
    public static final String SORT_BY_ID = "id";
    public static final String SORT_BY_JOINED_AT = "joinedAt";

    private CommonPaginationConstants() {
    }
}
