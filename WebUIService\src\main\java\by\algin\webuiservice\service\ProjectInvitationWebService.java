package by.algin.webuiservice.service;

import by.algin.dto.project.CreateInvitationRequest;
import by.algin.dto.project.InvitationResponse;
import by.algin.dto.project.ProjectMemberResponse;
import by.algin.dto.response.ApiResponse;
import by.algin.dto.response.UserResponse;
import by.algin.webuiservice.client.ProjectServiceClient;
import by.algin.webuiservice.client.UserServiceClient;
import by.algin.webuiservice.constants.ModelAttributeConstants;
import by.algin.webuiservice.util.ModelUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectInvitationWebService {

    private final ProjectServiceClient projectServiceClient;
    private final UserServiceClient userServiceClient;
    
    public boolean sendInvitation(String projectId, CreateInvitationRequest request,
                                Authentication authentication, Model model) {

        Optional<UserResponse> userOpt = validateUserForInvitationSending(authentication, model);
        if (userOpt.isEmpty()) {
            return false;
        }

        return processInvitationSending(projectId, request, model);
    }

    private Optional<UserResponse> validateUserForInvitationSending(Authentication authentication, Model model) {
        Optional<UserResponse> userOpt = getCurrentUser(authentication);
        if (userOpt.isEmpty()) {
            log.error("User not found for authentication: {}", authentication != null ? authentication.getName() : "null");
            ModelUtils.addError(model, "User not found");
        }
        return userOpt;
    }

    private boolean processInvitationSending(String projectId, CreateInvitationRequest request, Model model) {
        try {
            log.info("Sending invitation for project {} to email {}", projectId, request.getEmail());

            InvitationResponse invitation = projectServiceClient.createInvitation(
                    Long.parseLong(projectId), request);
            return handleInvitationSendingResponse(invitation, request, model);

        } catch (Exception e) {
            return handleInvitationSendingError(projectId, e, model);
        }
    }

    private boolean handleInvitationSendingResponse(InvitationResponse invitation, CreateInvitationRequest request, Model model) {
        if (invitation != null) {
            log.info("Successfully created invitation with ID {}", invitation.getId());
            String successMessage = buildInvitationSuccessMessage(request, invitation);
            ModelUtils.addSuccess(model, successMessage);
            return true;
        } else {
            ModelUtils.addError(model, "Failed to send invitation");
            return false;
        }
    }

    private String buildInvitationSuccessMessage(CreateInvitationRequest request, InvitationResponse invitation) {
        return request.getEmail() != null && !request.getEmail().trim().isEmpty()
            ? "Invitation created for " + request.getEmail() + ". Share this link: " + invitation.getInvitationUrl()
            : "Invitation link created. Share this link: " + invitation.getInvitationUrl();
    }

    private boolean handleInvitationSendingError(String projectId, Exception e, Model model) {
        log.error("Error sending invitation for project {}: {}", projectId, e.getMessage(), e);
        ModelUtils.addError(model, "Failed to send invitation: " + e.getMessage());
        return false;
    }
 
    public void prepareJoinForm(String token, Authentication authentication, Model model) {
        try {
            log.info("Preparing join form for token: {}", token);

            InvitationResponse invitation = loadInvitation(token);
            if (invitation != null) {
                setupJoinFormData(invitation, token, authentication, model);
                log.info("Prepared join form for project: {}", invitation.getProjectName());
            } else {
                ModelUtils.addError(model, "Invalid or expired invitation");
            }

        } catch (Exception e) {
            handleJoinFormError(token, e, model);
        }
    }

    private InvitationResponse loadInvitation(String token) {
        return projectServiceClient.getInvitationByToken(token);
    }

    private void setupJoinFormData(InvitationResponse invitation, String token, Authentication authentication, Model model) {
        ModelUtils.addInvitation(model, invitation);
        model.addAttribute("token", token);

        processAuthenticationForJoinForm(invitation, authentication, model);
    }

    private void processAuthenticationForJoinForm(InvitationResponse invitation, Authentication authentication, Model model) {
        if (authentication != null && authentication.isAuthenticated()) {
            setupAuthenticatedUserData(invitation, authentication, model);
        } else {
            model.addAttribute("needsAuth", true);
        }
    }

    private void setupAuthenticatedUserData(InvitationResponse invitation, Authentication authentication, Model model) {
        Optional<UserResponse> userOpt = getCurrentUser(authentication);
        if (userOpt.isPresent()) {
            UserResponse user = userOpt.get();
            ModelUtils.addUser(model, user);

            checkInvitationEmailMismatch(invitation, user, model);
        }
    }

    private void checkInvitationEmailMismatch(InvitationResponse invitation, UserResponse user, Model model) {
        String invitedEmail = invitation.getInvitedEmail();
        if (invitedEmail != null && !invitedEmail.trim().isEmpty()) {
            if (!invitedEmail.equalsIgnoreCase(user.getEmail())) {
                String note = "This invitation was originally intended for " + invitedEmail +
                             ", but anyone can use this link to join the project.";
                model.addAttribute("invitationNote", note);
            }
        }
    }

    private void handleJoinFormError(String token, Exception e, Model model) {
        log.error("Error preparing join form for token {}: {}", token, e.getMessage(), e);
        ModelUtils.addError(model, "Failed to load invitation details");
    }

    public boolean acceptInvitation(String token, Authentication authentication, Model model) {
        Optional<UserResponse> userOpt = validateUserForInvitation(authentication, model);
        if (userOpt.isEmpty()) {
            return false;
        }

        return processInvitationAcceptance(token, model);
    }

    private Optional<UserResponse> validateUserForInvitation(Authentication authentication, Model model) {
        Optional<UserResponse> userOpt = getCurrentUser(authentication);
        if (userOpt.isEmpty()) {
            log.error("User not found for authentication: {}", authentication != null ? authentication.getName() : "null");
            ModelUtils.addError(model, "Please log in to accept the invitation");
        }
        return userOpt;
    }

    private boolean processInvitationAcceptance(String token, Model model) {
        try {
            log.info("Accepting invitation with token: {}", token);

            ProjectMemberResponse member = projectServiceClient.acceptInvitation(token);
            return handleInvitationResponse(member, model);

        } catch (Exception e) {
            return handleInvitationError(token, e, model);
        }
    }

    private boolean handleInvitationResponse(ProjectMemberResponse member, Model model) {
        if (member != null) {
            log.info("Successfully accepted invitation and joined project {}", member.getProjectId());
            ModelUtils.addSuccess(model, "Successfully joined the project as " + member.getRole());
            return true;
        } else {
            ModelUtils.addError(model, "Failed to accept invitation");
            return false;
        }
    }

    private boolean handleInvitationError(String token, Exception e, Model model) {
        log.error("Error accepting invitation with token {}: {}", token, e.getMessage(), e);
        ModelUtils.addError(model, "Failed to accept invitation: " + e.getMessage());
        return false;
    }

    private Optional<UserResponse> getCurrentUser(Authentication authentication) {
        if (authentication == null || !authentication.isAuthenticated() ||
            "anonymousUser".equals(authentication.getName())) {
            return Optional.empty();
        }

        String username = authentication.getName();
        try {
            ApiResponse<UserResponse> apiResponse = userServiceClient.searchUsers("username", username);
            if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                return Optional.of(apiResponse.getData());
            } else {
                log.warn("User not found for username: {}", username);
                return Optional.empty();
            }
        } catch (Exception e) {
            log.error("Error retrieving user for username: {}, error: {}", username, e.getMessage());
            return Optional.empty();
        }
    }
}
