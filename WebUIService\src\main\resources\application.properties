# Spring Boot Configuration
spring.application.name=WEB-UI-SERVICE
server.port=8082

# Eureka Client Configuration
eureka.client.serviceUrl.defaultZone=http://localhost:8761/eureka/
eureka.instance.hostname=localhost

# External Service URLs
user-service.url=http://localhost:8081
project-service.url=http://localhost:8083

# JWT Configuration (using environment variables)
app.security.jwt.secret-key=${JWT_SECRET_KEY:mySecretKey123456789012345678901234567890}
app.security.jwt.default-expiry-seconds=${JWT_ACCESS_TOKEN_EXPIRATION_SECONDS:900}
app.security.jwt.refresh-token.expiry-seconds=${JWT_REFRESH_TOKEN_EXPIRATION_SECONDS:604800}

# Cookie Configuration
app.security.cookies.jwt-cookie-name=jwt
app.security.cookies.refresh-jwt-cookie-name=refresh_jwt
app.security.cookies.jsession-id-cookie-name=JSESSIONID

# Locale Configuration
# Cache seconds for message source (-1 = no cache, 0 = cache forever, >0 = cache for specified seconds)
# For development: -1 (no cache for hot reload), for production: 3600 (1 hour cache)
app.locale.cache-seconds=-1

# Logging Configuration
logging.level.root=INFO
logging.level.by.algin.webuiservice=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.springframework.boot=DEBUG
logging.level.feign=DEBUG