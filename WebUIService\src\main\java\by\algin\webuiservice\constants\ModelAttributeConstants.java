package by.algin.webuiservice.constants;

public final class ModelAttributeConstants {

    public static final String ERROR_ATTRIBUTE = "error";
    public static final String SUCCESS_ATTRIBUTE = "success";
    public static final String USER_ATTRIBUTE = "user";
    public static final String INVITATION_ATTRIBUTE = "invitation";
    public static final String PROJECT_ATTRIBUTE = "project";
    public static final String PROJECTS_ATTRIBUTE = "projects";
    public static final String MEMBERS_ATTRIBUTE = "members";

    public static final String MESSAGE_ATTRIBUTE = "message";
    public static final String USER_EMAIL_ATTRIBUTE = "userEmail";
    public static final String EXPIRED_TOKEN_ATTRIBUTE = "expiredToken";
    public static final String EMAIL_ATTRIBUTE = "email";
    public static final String TOKEN_ATTRIBUTE = "token";

    public static final String ANONYMOUS_USER = "anonymousUser";

    private ModelAttributeConstants() {
    }
}
