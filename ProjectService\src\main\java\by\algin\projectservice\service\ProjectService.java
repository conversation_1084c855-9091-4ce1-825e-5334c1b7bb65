package by.algin.projectservice.service;

import by.algin.dto.common.PaginationParams;
import by.algin.dto.response.UserResponse;
import by.algin.dto.response.PagedResponse;
import by.algin.dto.project.*;

import by.algin.projectservice.exception.*;
import by.algin.projectservice.entity.Project;
import by.algin.projectservice.entity.ProjectMember;
import by.algin.projectservice.entity.ProjectRole;
import by.algin.projectservice.repository.ProjectMemberRepository;
import by.algin.projectservice.repository.ProjectRepository;
import by.algin.projectservice.util.PaginationHelper;
import by.algin.projectservice.util.UserBaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProjectService {

    private final ProjectRepository projectRepository;
    private final ProjectMemberRepository projectMemberRepository;
    private final ProjectPermissionService permissionService;
    private final ProjectStatusService statusService;
    private final ProjectBusinessRulesService businessRulesService;
    private final MetricsService metricsService;
    private final UserBaseService userBaseService;
    private final ProjectRoleService roleService;

    @Transactional
    public ProjectResponse createProject(CreateProjectRequest request, Long currentUserId) {
        log.debug("Creating project '{}' for user {}", request.getName(), currentUserId);
        Long ownerId = currentUserId;
        UserResponse owner = userBaseService.getUserByIdOrThrow(ownerId);
        log.debug("Validated owner: {}", owner.getUsername());
        if (projectRepository.existsByNameIgnoreCase(request.getName())) {
            throw new DuplicateProjectNameException(request.getName());
        }
        Project project = Project.builder()
                .name(request.getName())
                .description(request.getDescription())
                .status(request.getStatus() != null ? request.getStatus() : ProjectStatus.ACTIVE)
                .ownerId(ownerId)
                .build();
        project = projectRepository.save(project);
        ProjectRole ownerRole = roleService.getOwnerRole();
        ProjectMember ownerMember = ProjectMember.builder()
                .project(project)
                .userId(ownerId)
                .role(ownerRole)
                .build();
        projectMemberRepository.save(ownerMember);
        metricsService.recordProjectCreation(project.getId(), ownerId);
        by.algin.dto.project.ProjectRole ownerRoleDto = by.algin.dto.project.ProjectRole.valueOf(ownerRole.getRoleName());
        metricsService.recordMemberAddition(project.getId(), ownerId, ownerRoleDto);
        log.debug("Successfully created project {} with ID {}", project.getName(), project.getId());
        return convertToProjectResponse(project, owner);
    }

    @Transactional(readOnly = true)
    public ProjectResponse getProjectById(Long projectId, Long currentUserId) {
        log.debug("Getting project {} for user {}", projectId, currentUserId);

        userBaseService.getUserByIdOrThrow(currentUserId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ProjectNotFoundException(projectId));

        validateProjectAccess(project, currentUserId);

        UserResponse owner = userBaseService.getUserByIdOrThrow(project.getOwnerId());

        return convertToProjectResponse(project, owner);
    }

    @Transactional
    public ProjectResponse updateProject(Long projectId, UpdateProjectRequest request, Long currentUserId) {
        log.debug("Updating project {} by user {}", projectId, currentUserId);

        userBaseService.getUserByIdOrThrow(currentUserId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ProjectNotFoundException(projectId));

        businessRulesService.validateProjectCanBeModified(project);

        validateProjectManagementAccess(project, currentUserId);

        if (request.getName() != null && !request.getName().equals(project.getName())) {
            if (projectRepository.existsByNameIgnoreCaseAndIdNot(request.getName(), projectId)) {
                throw new DuplicateProjectNameException(request.getName());
            }
            project.setName(request.getName());
        }

        if (request.getDescription() != null) {
            project.setDescription(request.getDescription());
        }
        if (request.getStatus() != null) {
            statusService.validateTransition(project.getStatus(), request.getStatus());
            project.setStatus(request.getStatus());
        }

        project = projectRepository.save(project);

        UserResponse owner = userBaseService.getUserByIdOrThrow(project.getOwnerId());

        log.debug("Successfully updated project {}", projectId);
        return convertToProjectResponse(project, owner);
    }

    @Transactional
    public void deleteProject(Long projectId, Long currentUserId) {
        log.debug("Deleting project {} by user {}", projectId, currentUserId);

        userBaseService.getUserByIdOrThrow(currentUserId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ProjectNotFoundException(projectId));

        businessRulesService.validateProjectDeletion(project, currentUserId);

        projectRepository.delete(project);

        metricsService.recordProjectDeletion(project.getId(), project.getOwnerId());

        log.debug("Successfully deleted project {}", projectId);
    }

    @Transactional(readOnly = true)
    public List<ProjectResponse> getUserProjects(Long userId) {
        log.debug("Getting projects for user {}", userId);

        List<Project> projects = projectRepository.findProjectsByMemberUserId(userId);

        List<Long> ownerIds = projects.stream()
                .map(Project::getOwnerId)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, UserResponse> owners = userBaseService.getUsersByIds(ownerIds)
                .stream()
                .collect(Collectors.toMap(UserResponse::getId, user -> user));

        return projects.stream()
                .map(project -> convertToProjectResponse(project, owners.get(project.getOwnerId())))
                .collect(Collectors.toList());
    }

    @Transactional
    public ProjectMemberResponse addProjectMember(Long projectId, AddProjectMemberRequest request, Long currentUserId) {
        log.debug("Adding user {} to project {} with role {} by user {}",
                request.getUserId(), projectId, request.getRole(), currentUserId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ProjectNotFoundException(projectId));

        validateProjectManagementAccess(project, currentUserId);

        Long userId = request.getUserId();

        by.algin.dto.project.ProjectRole requestRoleDto = request.getRole() != null ?
            request.getRole() : by.algin.dto.project.ProjectRole.DEVELOPER;
        ProjectRole role = roleService.getRoleByNameOrThrow(requestRoleDto.name());

        UserResponse user = userBaseService.getUserByIdOrThrow(userId);

        businessRulesService.validateMemberAddition(project, userId, role);

        ProjectMember member = ProjectMember.builder()
                .project(project)
                .userId(userId)
                .role(role)
                .build();

        member = projectMemberRepository.save(member);

        metricsService.recordMemberAddition(project.getId(), userId, requestRoleDto);

        log.debug("Successfully added user {} to project {}", request.getUserId(), projectId);
        return convertToProjectMemberResponse(member, user);
    }

    @Transactional
    public ProjectMemberResponse updateProjectMemberRole(Long projectId, Long userId,
                                                        UpdateProjectMemberRoleRequest request, Long currentUserId) {
        log.debug("Updating role of user {} in project {} to {} by user {}",
                userId, projectId, request.getRole(), currentUserId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ProjectNotFoundException(projectId));

        validateProjectManagementAccess(project, currentUserId);

        ProjectMember member = projectMemberRepository.findByProjectIdAndUserId(projectId, userId)
                .orElseThrow(() -> new ProjectMemberNotFoundException(projectId, userId));

        by.algin.dto.project.ProjectRole newRoleDto = request.getRole();
        ProjectRole newRole = roleService.getRoleByNameOrThrow(newRoleDto.name());

        businessRulesService.validateMemberRoleChange(project, member, newRole, currentUserId);

        member.setRole(newRole);
        member = projectMemberRepository.save(member);

        UserResponse user = userBaseService.getUserByIdOrThrow(userId);

        log.debug("Successfully updated role of user {} in project {}", userId, projectId);
        return convertToProjectMemberResponse(member, user);
    }

    @Transactional
    public void removeProjectMember(Long projectId, Long userId, Long currentUserId) {
        log.debug("Removing user {} from project {} by user {}", userId, projectId, currentUserId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ProjectNotFoundException(projectId));

        validateProjectManagementAccess(project, currentUserId);

        ProjectMember member = projectMemberRepository.findByProjectIdAndUserId(projectId, userId)
                .orElseThrow(() -> new ProjectMemberNotFoundException(projectId, userId));

        businessRulesService.validateMemberRemoval(member, currentUserId);

        by.algin.dto.project.ProjectRole memberRoleDto = by.algin.dto.project.ProjectRole.valueOf(member.getRole().getRoleName());

        projectMemberRepository.delete(member);

        metricsService.recordMemberRemoval(project.getId(), userId, memberRoleDto);

        log.debug("Successfully removed user {} from project {}", userId, projectId);
    }

    @Transactional(readOnly = true)
    public List<ProjectMemberResponse> getProjectMembers(Long projectId, Long currentUserId) {
        log.debug("Getting members of project {} for user {}", projectId, currentUserId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ProjectNotFoundException(projectId));

        validateProjectAccess(project, currentUserId);

        List<ProjectMember> members = projectMemberRepository.findByProjectIdOrderByJoinedAtAsc(projectId);

        List<Long> userIds = members.stream()
                .map(ProjectMember::getUserId)
                .collect(Collectors.toList());

        Map<Long, UserResponse> users = userBaseService.getUsersByIds(userIds)
                .stream()
                .collect(Collectors.toMap(UserResponse::getId, user -> user));

        return members.stream()
                .map(member -> convertToProjectMemberResponse(member, users.get(member.getUserId())))
                .collect(Collectors.toList());
    }

    private void validateProjectAccess(Project project, Long userId) {
        if (!projectMemberRepository.existsByProjectIdAndUserId(project.getId(), userId)) {
            throw new ProjectAccessDeniedException(userId, project.getId());
        }
    }

    private void validateProjectManagementAccess(Project project, Long userId) {
        if (!permissionService.canManageMembers(project.getId(), userId)) {
            throw new ProjectAccessDeniedException(userId, project.getId());
        }
    }

    private ProjectResponse convertToProjectResponse(Project project, UserResponse owner) {
        return ProjectResponse.builder()
                .id(project.getId())
                .name(project.getName())
                .description(project.getDescription())
                .status(project.getStatus())
                .ownerId(project.getOwnerId())
                .createdAt(project.getCreatedAt())
                .updatedAt(project.getUpdatedAt())
                .build();
    }

    private ProjectMemberResponse convertToProjectMemberResponse(ProjectMember member, UserResponse user) {
        by.algin.dto.project.ProjectRole roleDto = by.algin.dto.project.ProjectRole.valueOf(member.getRole().getRoleName());

        return ProjectMemberResponse.builder()
                .userId(member.getUserId())
                .username(user != null ? user.getUsername() : "Unknown User")
                .email(user != null ? user.getEmail() : null)
                .role(roleDto)
                .joinedAt(member.getJoinedAt())
                .build();
    }

    @Transactional(readOnly = true)
    public PagedResponse<ProjectResponse> getUserProjects(Long userId, Pageable pageable) {
        log.info("Getting projects for user {} with pagination: page={}, size={}",
                userId, pageable.getPageNumber(), pageable.getPageSize());

        userBaseService.getUserByIdOrThrow(userId);

        Page<Project> projectsPage = projectRepository.findByUserAccess(userId, pageable);

        List<Project> projects = projectsPage.getContent();
        List<Long> ownerIds = projects.stream()
                .map(Project::getOwnerId)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, UserResponse> owners = userBaseService.getUsersByIdsWithFallback(ownerIds);

        Page<ProjectResponse> responsePage = projectsPage.map(project -> {
            UserResponse owner = owners.get(project.getOwnerId());
            if (owner == null) {
                log.warn("Owner not found for project {}, owner ID: {} - using fallback",
                        project.getId(), project.getOwnerId());
            }
            return convertToProjectResponse(project, owner);
        });

        return PaginationHelper.toPagedResponse(responsePage);
    }

    @Transactional(readOnly = true)
    public PagedResponse<ProjectMemberResponse> getProjectMembers(Long projectId, Long currentUserId, Pageable pageable) {
        log.info("Getting members for project {} with pagination: page={}, size={}",
                projectId, pageable.getPageNumber(), pageable.getPageSize());

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ProjectNotFoundException(projectId));

        if (!project.getOwnerId().equals(currentUserId) &&
            !projectMemberRepository.existsByProjectIdAndUserId(projectId, currentUserId)) {
            throw new ProjectAccessDeniedException(currentUserId, projectId);
        }

        Page<ProjectMember> membersPage = projectMemberRepository.findByProjectIdOrderByJoinedAtAsc(projectId, pageable);

        List<ProjectMember> members = membersPage.getContent();
        List<Long> userIds = members.stream()
                .map(ProjectMember::getUserId)
                .collect(Collectors.toList());

        Map<Long, UserResponse> users = userBaseService.getUsersByIdsWithFallback(userIds);

        Page<ProjectMemberResponse> responsePage = membersPage.map(member -> {
            UserResponse user = users.get(member.getUserId());
            if (user == null) {
                log.warn("User not found for member {}, user ID: {}", member.getId(), member.getUserId());
            }
            return convertToProjectMemberResponse(member, user);
        });

        return PaginationHelper.toPagedResponse(responsePage);
    }

    @Transactional(readOnly = true)
    public PaginationHelper.PagedResponseWithParams<ProjectMemberResponse> getProjectMembersWithParams(Long projectId, Long currentUserId, PaginationParams paginationParams) {
        log.info("Getting members for project {} with pagination params: page={}, size={}, sortBy={}, sortDir={}",
                projectId, paginationParams.getPage(), paginationParams.getSize(),
                paginationParams.getSortBy(), paginationParams.getSortDir());

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ProjectNotFoundException(projectId));

        if (!project.getOwnerId().equals(currentUserId) &&
            !projectMemberRepository.existsByProjectIdAndUserId(projectId, currentUserId)) {
            throw new ProjectAccessDeniedException(currentUserId, projectId);
        }

        Pageable pageable = paginationParams.toPageable();
        Page<ProjectMember> membersPage = projectMemberRepository.findByProjectIdOrderByJoinedAtAsc(projectId, pageable);

        List<ProjectMember> members = membersPage.getContent();
        List<Long> userIds = members.stream()
                .map(ProjectMember::getUserId)
                .collect(Collectors.toList());

        Map<Long, UserResponse> users = userBaseService.getUsersByIdsWithFallback(userIds);

        Page<ProjectMemberResponse> responsePage = membersPage.map(member -> {
            UserResponse user = users.get(member.getUserId());
            if (user == null) {
                log.warn("User not found for member {}, user ID: {}", member.getId(), member.getUserId());
            }
            return convertToProjectMemberResponse(member, user);
        });

        return PaginationHelper.toPagedResponseWithParams(responsePage, paginationParams);
    }

    @Transactional(readOnly = true)
    public PaginationHelper.PagedResponseWithParams<ProjectResponse> getUserProjectsWithParams(Long userId, PaginationParams paginationParams) {
        log.debug("Getting projects for user {} with pagination params: page={}, size={}, sortBy={}, sortDir={}",
                userId, paginationParams.getPage(), paginationParams.getSize(),
                paginationParams.getSortBy(), paginationParams.getSortDir());

        Pageable pageable = paginationParams.toPageable();
        Page<Project> projectsPage = projectRepository.findByOwnerIdOrderByCreatedAtDesc(userId, pageable);

        List<Project> projects = projectsPage.getContent();
        List<Long> ownerIds = projects.stream()
                .map(Project::getOwnerId)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, UserResponse> owners = userBaseService.getUsersByIdsWithFallback(ownerIds);

        Page<ProjectResponse> responsePage = projectsPage.map(project -> {
            UserResponse owner = owners.get(project.getOwnerId());
            if (owner == null) {
                log.warn("Owner not found for project {}, owner ID: {} - using fallback",
                        project.getId(), project.getOwnerId());
            }
            return convertToProjectResponse(project, owner);
        });

        return PaginationHelper.toPagedResponseWithParams(responsePage, paginationParams);
    }

    @Transactional(readOnly = true)
    public PagedResponse<ProjectResponse> getProjectsByOwner(Long ownerId, Pageable pageable) {
        log.info("Getting projects by owner {} with pagination: page={}, size={}",
                ownerId, pageable.getPageNumber(), pageable.getPageSize());

        UserResponse owner = userBaseService.getUserByIdOrThrow(ownerId);

        Page<Project> projectsPage = projectRepository.findByOwnerIdOrderByCreatedAtDesc(ownerId, pageable);

        Page<ProjectResponse> responsePage = projectsPage.map(project -> convertToProjectResponse(project, owner));
        return PaginationHelper.toPagedResponse(responsePage);
    }

    @Transactional(readOnly = true)
    public PagedResponse<ProjectResponse> getProjectsByStatus(ProjectStatus status, Pageable pageable) {
        log.info("Getting projects by status {} with pagination: page={}, size={}",
                status, pageable.getPageNumber(), pageable.getPageSize());

        Page<Project> projectsPage = projectRepository.findByStatusOrderByCreatedAtDesc(status, pageable);

        List<Project> projects = projectsPage.getContent();
        List<Long> ownerIds = projects.stream()
                .map(Project::getOwnerId)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, UserResponse> owners = userBaseService.getUsersByIdsWithFallback(ownerIds);

        Page<ProjectResponse> responsePage = projectsPage.map(project -> {
            UserResponse owner = owners.get(project.getOwnerId());
            if (owner == null) {
                log.warn("Owner not found for project {}, owner ID: {} - using fallback",
                        project.getId(), project.getOwnerId());
            }
            return convertToProjectResponse(project, owner);
        });

        return PaginationHelper.toPagedResponse(responsePage);
    }

    public boolean hasProjectAccess(Long projectId, Long userId, List<ProjectRole> requiredRoles) {
        try {
            ProjectMember member = projectMemberRepository.findByProjectIdAndUserId(projectId, userId)
                    .orElse(null);

            if (member == null) {
                return false;
            }

            return requiredRoles.contains(member.getRole());
        } catch (Exception e) {
            log.debug("Error checking project access for user {} in project {}: {}", userId, projectId, e.getMessage());
            return false;
        }
    }

    public List<ProjectRole> getAllAssignableRoles() {
        log.debug("Getting all assignable project roles");
        return roleService.getAssignableRoles();
    }

}
