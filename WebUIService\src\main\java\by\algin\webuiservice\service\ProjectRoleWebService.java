package by.algin.webuiservice.service;

import by.algin.dto.project.ProjectRoleResponse;
import by.algin.dto.response.ApiResponse;
import by.algin.webuiservice.client.ProjectServiceClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProjectRoleWebService {

    private final ProjectServiceClient projectServiceClient;

    public List<ProjectRoleResponse> getAvailableRoles() {
        try {
            log.debug("Fetching available project roles from ProjectService");
            ApiResponse<List<ProjectRoleResponse>> response = projectServiceClient.getAvailableRoles();
            
            if (response != null && response.isSuccess() && response.getData() != null) {
                List<ProjectRoleResponse> roles = response.getData();
                log.info("Successfully fetched {} project roles", roles.size());
                return roles;
            } else {
                log.warn("Failed to fetch project roles: {}", 
                    response != null ? response.getMessage() : "null response");
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("Error fetching project roles from ProjectService", e);
            return Collections.emptyList();
        }
    }

    public List<ProjectRoleResponse> getAssignableRoles() {
        try {
            log.debug("Fetching assignable project roles from ProjectService");
            ApiResponse<List<ProjectRoleResponse>> response = projectServiceClient.getAssignableRoles();

            if (response != null && response.isSuccess() && response.getData() != null) {
                List<ProjectRoleResponse> roles = response.getData();
                log.info("Successfully fetched {} assignable project roles", roles.size());
                return roles;
            } else {
                log.warn("Failed to fetch assignable project roles: {}",
                    response != null ? response.getMessage() : "null response");
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("Error fetching assignable project roles from ProjectService", e);
            return Collections.emptyList();
        }
    }
}
