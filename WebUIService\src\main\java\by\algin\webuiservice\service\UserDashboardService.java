package by.algin.webuiservice.service;

import by.algin.dto.response.ApiResponse;
import by.algin.dto.response.UserResponse;
import by.algin.webuiservice.client.UserServiceClient;
import by.algin.webuiservice.constants.ModelAttributeConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;

import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserDashboardService {

    private final UserServiceClient userServiceClient;

    public void prepareDashboard(Authentication authentication, Model model) {
        Optional<UserResponse> user = getCurrentUser(authentication);
        addUserToModel(model, user);
        
        if (user.isPresent()) {
            log.info("User {} accessed dashboard", user.get().getUsername());
        } else {
            log.warn("Dashboard accessed without valid user");
        }
    }

    public void prepareAdminDashboard(Authentication authentication, Model model) {
        Optional<UserResponse> user = getCurrentUser(authentication);
        addUserToModel(model, user);
        
        if (user.isPresent()) {
            log.info("Admin {} accessed admin dashboard", user.get().getUsername());
        } else {
            log.warn("Admin dashboard accessed without valid user");
        }
    }

    private void addUserToModel(Model model, Optional<UserResponse> user) {
        model.addAttribute(ModelAttributeConstants.USER_ATTRIBUTE, user.orElse(null));
    }

    private Optional<UserResponse> getCurrentUser(Authentication authentication) {
        if (!isValidAuthentication(authentication)) {
            return Optional.empty();
        }

        String username = authentication.getName();
        log.debug("Retrieving user info for: {}", username);

        try {
            ApiResponse<UserResponse> apiResponse = userServiceClient.searchUsers("username", username);
            if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                UserResponse user = apiResponse.getData();
                enrichUserWithAuthenticationData(user, authentication);
                log.debug("Successfully retrieved user: {}", user.getUsername());
                return Optional.of(user);
            } else {
                log.warn("User not found for username: {}", username);
                return Optional.empty();
            }
        } catch (Exception e) {
            log.error("Error retrieving user for username: {}, error: {}", username, e.getMessage());
            return Optional.empty();
        }
    }

    private void enrichUserWithAuthenticationData(UserResponse user, Authentication authentication) {
        if (user.getUsername() == null) {
            user.setUsername(authentication.getName());
        }
        if (user.getEmail() == null) {
            user.setEmail(authentication.getName());
        }
        if (user.getRoles() == null) {
            user.setRoles(extractRolesFromAuthentication(authentication));
        }
    }

    private Set<String> extractRolesFromAuthentication(Authentication authentication) {
        return authentication.getAuthorities().stream()
                .map(authority -> authority.getAuthority())
                .collect(Collectors.toSet());
    }

    private boolean isValidAuthentication(Authentication authentication) {
        return authentication != null
                && authentication.isAuthenticated()
                && !isAnonymousUser(authentication.getName());
    }

    private boolean isAnonymousUser(String username) {
        return ModelAttributeConstants.ANONYMOUS_USER.equals(username);
    }
}
