package by.algin.projectservice.repository;

import by.algin.dto.project.ProjectStatus;
import by.algin.projectservice.entity.Project;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProjectRepository extends JpaRepository<Project, Long>, JpaSpecificationExecutor<Project> {

    List<Project> findByOwnerIdOrderByCreatedAtDesc(Long ownerId);
    Page<Project> findByOwnerIdOrderByCreatedAtDesc(Long ownerId, Pageable pageable);

    List<Project> findByStatusOrderByCreatedAtDesc(ProjectStatus status);
    Page<Project> findByStatusOrderByCreatedAtDesc(ProjectStatus status, Pageable pageable);

    List<Project> findByNameContainingIgnoreCaseOrderByCreatedAtDesc(String name);
    Page<Project> findByNameContainingIgnoreCaseOrderByCreatedAtDesc(String name, Pageable pageable);

    List<Project> findByOwnerIdAndStatusOrderByCreatedAtDesc(Long ownerId, ProjectStatus status);
    Page<Project> findByOwnerIdAndStatusOrderByCreatedAtDesc(Long ownerId, ProjectStatus status, Pageable pageable);

    List<Project> findByStatusInOrderByCreatedAtDesc(List<ProjectStatus> statuses);
    Page<Project> findByStatusInOrderByCreatedAtDesc(List<ProjectStatus> statuses, Pageable pageable);

    @Query("SELECT DISTINCT p FROM Project p JOIN p.members m WHERE m.userId = :userId ORDER BY p.createdAt DESC")
    List<Project> findProjectsByMemberUserId(@Param("userId") Long userId);

    @Query("SELECT DISTINCT p FROM Project p JOIN p.members m WHERE m.userId = :userId ORDER BY p.createdAt DESC")
    Page<Project> findProjectsByMemberUserId(@Param("userId") Long userId, Pageable pageable);

    @Query("SELECT p FROM Project p ORDER BY p.createdAt DESC")
    List<Project> findAllOrderByCreatedAtDesc();

    @Query("SELECT DISTINCT p FROM Project p LEFT JOIN p.members m WHERE p.ownerId = :userId OR m.userId = :userId")
    Page<Project> findByUserAccess(@Param("userId") Long userId, Pageable pageable);

    boolean existsByNameIgnoreCase(String name);

    @Query("SELECT COUNT(p) > 0 FROM Project p WHERE LOWER(p.name) = LOWER(:name) AND p.id != :excludeId")
    boolean existsByNameIgnoreCaseAndIdNot(@Param("name") String name, @Param("excludeId") Long excludeId);

    long countByOwnerId(Long ownerId);

    long countByStatus(ProjectStatus status);

    @Query("SELECT COUNT(DISTINCT p) FROM Project p JOIN p.members m WHERE m.userId = :userId")
    long countProjectsByMemberUserId(@Param("userId") Long userId);
}
