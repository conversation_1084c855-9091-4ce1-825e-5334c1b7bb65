package by.algin.webuiservice.util;

import by.algin.dto.response.UserResponse;
import by.algin.webuiservice.constants.ModelAttributeConstants;
import lombok.experimental.UtilityClass;
import org.springframework.ui.Model;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

@UtilityClass
public class ModelUtils {

    public static void addError(Model model, String message) {
        if (model != null && message != null) {
            model.addAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, message);
        }
    }

    public static void addSuccess(Model model, String message) {
        if (model != null && message != null) {
            model.addAttribute(ModelAttributeConstants.SUCCESS_ATTRIBUTE, message);
        }
    }

    public static void addUser(Model model, UserResponse user) {
        if (model != null && user != null) {
            model.addAttribute(ModelAttributeConstants.USER_ATTRIBUTE, user);
        }
    }

    public static void addProject(Model model, Object project) {
        if (model != null && project != null) {
            model.addAttribute(ModelAttributeConstants.PROJECT_ATTRIBUTE, project);
        }
    }

    public static void addProjects(Model model, Object projects) {
        if (model != null && projects != null) {
            model.addAttribute(ModelAttributeConstants.PROJECTS_ATTRIBUTE, projects);
        }
    }

    public static void addMembers(Model model, Object members) {
        if (model != null && members != null) {
            model.addAttribute(ModelAttributeConstants.MEMBERS_ATTRIBUTE, members);
        }
    }

    public static void addInvitation(Model model, Object invitation) {
        if (model != null && invitation != null) {
            model.addAttribute(ModelAttributeConstants.INVITATION_ATTRIBUTE, invitation);
        }
    }

    public static void addMessage(Model model, String message) {
        if (model != null && message != null) {
            model.addAttribute(ModelAttributeConstants.MESSAGE_ATTRIBUTE, message);
        }
    }

    public static void addUserEmail(Model model, String email) {
        if (model != null && email != null) {
            model.addAttribute(ModelAttributeConstants.USER_EMAIL_ATTRIBUTE, email);
        }
    }

    public static void addErrorToRedirect(RedirectAttributes redirectAttributes, String message) {
        if (redirectAttributes != null && message != null) {
            redirectAttributes.addFlashAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE, message);
        }
    }

    public static void addSuccessToRedirect(RedirectAttributes redirectAttributes, String message) {
        if (redirectAttributes != null && message != null) {
            redirectAttributes.addFlashAttribute(ModelAttributeConstants.SUCCESS_ATTRIBUTE, message);
        }
    }

    public static boolean hasError(Model model) {
        return model != null && model.containsAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE);
    }

    public static boolean hasSuccess(Model model) {
        return model != null && model.containsAttribute(ModelAttributeConstants.SUCCESS_ATTRIBUTE);
    }

    public static String getError(Model model) {
        if (model != null && model.containsAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE)) {
            return (String) model.getAttribute(ModelAttributeConstants.ERROR_ATTRIBUTE);
        }
        return null;
    }

    public static String getSuccess(Model model) {
        if (model != null && model.containsAttribute(ModelAttributeConstants.SUCCESS_ATTRIBUTE)) {
            return (String) model.getAttribute(ModelAttributeConstants.SUCCESS_ATTRIBUTE);
        }
        return null;
    }

    public static Model createTemporaryModel() {
        return new org.springframework.ui.ExtendedModelMap();
    }
}
