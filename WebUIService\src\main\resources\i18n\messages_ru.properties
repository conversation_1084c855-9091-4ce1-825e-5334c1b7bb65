
# Common
language.english=Английский
language.russian=Русский

# Navigation
nav.home=Главная
nav.logout=Выход

# Homepage
homepage.title=Веб-интерфейс
homepage.welcome=Добро пожаловать в Task Helper

# Authentication
auth.login=Вход
auth.register=Регистрация
auth.email=Электронная почта
auth.password=Пароль
auth.confirm.password=Подтвердите пароль
auth.username=Имя пользователя
auth.login.title=Вход в систему
auth.register.title=Регистрация
auth.login.button=Войти
auth.register.button=Зарегистрироваться

# Dashboard
dashboard.success.title=Успешная аутентификация
dashboard.welcome.message=Добро пожаловать в систему управления задачами.
dashboard.hello=Привет

# Admin Dashboard
admin.dashboard.title=Панель администратора
admin.dashboard.welcome=Добро пожаловать в панель администратора
admin.dashboard.privileges=У вас есть права администратора.

# Errors
error.general=Произошла ошибка

# Login errors
error.login.invalid.credentials=Неверное имя пользователя или пароль
error.login.account.disabled=Аккаунт отключен
error.login.account.locked=Аккаунт заблокирован

# Registration errors
error.registration.email.exists=Email уже существует
error.registration.username.exists=Имя пользователя уже существует
error.registration.passwords.mismatch=Пароли не совпадают
error.registration.email.invalid=Неверный формат email

# Validation errors
error.validation.failed=Ошибка валидации

# Token errors
error.token.invalid=Неверный токен
error.token.expired=Срок действия токена истек

# General errors
error.user.not.found=Пользователь не найден
error.rate.limit.exceeded=Слишком много запросов, попробуйте позже
error.server.internal=Внутренняя ошибка сервера
error.service.unavailable=Сервис временно недоступен

# Success messages
success.registration=Регистрация прошла успешно

# Registration
registration.check.email=Пожалуйста, проверьте свою электронную почту для подтверждения аккаунта.
registration.success.message=Регистрация прошла успешно! Пожалуйста, проверьте свою электронную почту для подтверждения.

# Account confirmation
account.confirmed.title=Аккаунт подтвержден
account.confirmed.message=Ваш аккаунт был успешно подтвержден!

# Validation messages
validation.username.required=Имя пользователя или email обязательно
validation.username.size=Имя пользователя должно быть от 3 до 50 символов
validation.email.required=Email обязателен
validation.email.format=Email должен быть корректным
validation.password.required=Пароль обязателен
validation.password.size=Пароль должен быть не менее 6 символов
validation.confirmPassword.required=Подтверждение пароля обязательно

# Token management
token.expired.title=Токен истек
token.expired.message=Срок действия токена подтверждения аккаунта истек.
token.expired.instruction=Пожалуйста, нажмите на кнопку ниже, чтобы получить новый токен.
token.resend.button=Отправить новый токен

# Footer
footer.copyright=© 2025 Веб-интерфейс. Все права защищены.
