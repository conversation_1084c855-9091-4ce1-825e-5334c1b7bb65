package by.algin.projectservice.repository;

import by.algin.dto.project.ProjectStatus;
import by.algin.projectservice.entity.ProjectStatusTransition;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ProjectStatusTransitionRepository extends JpaRepository<ProjectStatusTransition, Long> {

    @Query("SELECT t FROM ProjectStatusTransition t WHERE " +
           "(:fromStatus IS NULL OR t.fromStatus = :fromStatus) " +
           "AND t.isActive = true " +
           "AND (t.validFrom IS NULL OR t.validFrom <= :now) " +
           "AND (t.validTo IS NULL OR t.validTo > :now)")
    List<ProjectStatusTransition> findActiveTransitionsFrom(
        @Param("fromStatus") ProjectStatus fromStatus,
        @Param("now") LocalDateTime now
    );

    @Query("SELECT t FROM ProjectStatusTransition t WHERE t.fromStatus = :fromStatus " +
           "AND t.toStatus = :toStatus " +
           "AND t.isActive = true " +
           "AND (t.validFrom IS NULL OR t.validFrom <= :now) " +
           "AND (t.validTo IS NULL OR t.validTo > :now)")
    Optional<ProjectStatusTransition> findActiveTransition(
        @Param("fromStatus") ProjectStatus fromStatus,
        @Param("toStatus") ProjectStatus toStatus,
        @Param("now") LocalDateTime now
    );

    List<ProjectStatusTransition> findAllByOrderByFromStatusAscToStatusAsc();

    List<ProjectStatusTransition> findByFromStatusOrderByToStatusAsc(ProjectStatus fromStatus);

    List<ProjectStatusTransition> findByRequiredRoleAndIsActiveTrue(String requiredRole);

    @Query("SELECT COUNT(t) > 0 FROM ProjectStatusTransition t WHERE t.fromStatus = :fromStatus " +
           "AND t.toStatus = :toStatus " +
           "AND t.isActive = true " +
           "AND (t.validFrom IS NULL OR t.validFrom <= :now) " +
           "AND (t.validTo IS NULL OR t.validTo > :now)")
    boolean existsActiveTransition(
        @Param("fromStatus") ProjectStatus fromStatus,
        @Param("toStatus") ProjectStatus toStatus,
        @Param("now") LocalDateTime now
    );

    List<ProjectStatusTransition> findByCreatedByOrderByCreatedAtDesc(String createdBy);

    Optional<ProjectStatusTransition> findByFromStatusAndToStatus(ProjectStatus fromStatus, ProjectStatus toStatus);

@Query("SELECT t FROM ProjectStatusTransition t WHERE t.isActive = true " +
           "AND t.validTo IS NOT NULL " +
           "AND t.validTo BETWEEN :now AND :expiryThreshold")
    List<ProjectStatusTransition> findExpiringTransitions(
        @Param("now") LocalDateTime now,
        @Param("expiryThreshold") LocalDateTime expiryThreshold
    );

@Query("SELECT DISTINCT t.fromStatus FROM ProjectStatusTransition t WHERE t.isActive = true " +
           "AND (t.validFrom IS NULL OR t.validFrom <= :now) " +
           "AND (t.validTo IS NULL OR t.validTo > :now)")
    List<ProjectStatus> findActiveFromStatuses(@Param("now") LocalDateTime now);

    @Query("SELECT t FROM ProjectStatusTransition t WHERE t.fromStatus = :fromStatus " +
           "AND t.isActive = true " +
           "AND (t.validFrom IS NULL OR t.validFrom <= :now) " +
           "AND (t.validTo IS NULL OR t.validTo > :now) " +
           "ORDER BY t.toStatus")
    List<ProjectStatusTransition> findActiveTransitionsFromWithPrefix(
        @Param("fromStatus") ProjectStatus fromStatus,
        @Param("now") LocalDateTime now
    );

    @Query("SELECT t FROM ProjectStatusTransition t WHERE t.fromStatus = :fromStatus " +
           "AND t.toStatus = :toStatus " +
           "AND t.isActive = true " +
           "AND (t.validFrom IS NULL OR t.validFrom <= :now) " +
           "AND (t.validTo IS NULL OR t.validTo > :now) " +
           "ORDER BY t.id")
    Optional<ProjectStatusTransition> findActiveTransitionWithPrefix(
        @Param("fromStatus") ProjectStatus fromStatus,
        @Param("toStatus") ProjectStatus toStatus,
        @Param("now") LocalDateTime now
    );
}
