<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="'Edit Project: ' + ${project?.name ?: 'Unknown'}">Edit Project</title>
    <link rel="stylesheet" th:href="@{/css/variables.css}">
    <link rel="stylesheet" th:href="@{/css/base.css}">
    <link rel="stylesheet" th:href="@{/css/forms.css}">
    <link rel="stylesheet" th:href="@{/css/buttons.css}">
    <link rel="stylesheet" th:href="@{/css/alerts.css}">
    <link rel="stylesheet" th:href="@{/css/projects.css}">
</head>
<body>
    <div class="language-switcher">
        <a th:href="@{/setLanguage(lang='en')}" th:text="#{language.english}">English</a> |
        <a th:href="@{/setLanguage(lang='ru')}" th:text="#{language.russian}">Russian</a>
    </div>

    <div class="container">
        <div th:if="${error}" class="alert alert-danger">
            <p th:text="${error}"></p>
        </div>
        <div th:if="${success}" class="alert alert-success">
            <p th:text="${success}"></p>
        </div>
        <div th:if="${project}" class="project-header edit">
            <h1 class="project-title edit">Edit Project</h1>
            <p class="text-muted no-margin">Editing: <strong th:text="${project.name}">Project Name</strong></p>
        </div>


        <div th:unless="${project}" class="alert alert-danger">
            <h2>Project Not Found</h2>
            <p>The requested project could not be found or you don't have permission to edit it.</p>
            <a th:href="@{/dashboard}" class="btn btn-secondary">Back to Dashboard</a>
        </div>


        <div th:if="${project}">
            <form th:action="@{/projects/{id}/edit(id=${project.id})}" th:object="${updateRequest}" method="post">
                <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}" />

                <div class="form-group">
                    <label for="name">Project Name</label>
                    <div class="current-value">
                        <div class="current-label">Current Name:</div>
                        <div class="current-text" th:text="${project.name}">Current project name</div>
                    </div>
                    <input type="text" 
                           id="name" 
                           th:field="*{name}" 
                           th:placeholder="${project.name}"
                           placeholder="Enter new project name (leave empty to keep current)" />
                    <div th:if="${#fields.hasErrors('name')}" class="error">
                        <span th:errors="*{name}"></span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="description">Description</label>
                    <div class="current-value">
                        <div class="current-label">Current Description:</div>
                        <div class="current-text" th:text="${project.description ?: 'No description'}">Current description</div>
                    </div>
                    <textarea id="description" 
                             th:field="*{description}" 
                             th:placeholder="${project.description ?: 'Enter project description'}"
                             placeholder="Enter new description (leave empty to keep current)"></textarea>
                    <div th:if="${#fields.hasErrors('description')}" class="error">
                        <span th:errors="*{description}"></span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="status">Project Status</label>
                    <div class="current-value">
                        <div class="current-label">Current Status:</div>
                        <div class="current-text" th:classappend="'status-' + ${#strings.toLowerCase(project.status)}">
                            <span th:text="${project.status}">Current status</span>
                        </div>
                    </div>
                    <select id="status" th:field="*{status}">
                        <option value="">Keep current status</option>
                        <option value="ACTIVE">Active</option>
                        <option value="INACTIVE">Inactive</option>
                        <option value="COMPLETED">Completed</option>
                        <option value="ARCHIVED">Archived</option>
                    </select>
                    <div th:if="${#fields.hasErrors('status')}" class="error">
                        <span th:errors="*{status}"></span>
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-warning">Update Project</button>
                    <a th:href="@{/projects/{id}(id=${project.id})}" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</body>
</html>
