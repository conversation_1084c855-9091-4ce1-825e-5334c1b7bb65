package by.algin.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Common utility for writing JSON responses
 * Used across all services for consistent error responses
 */
@Slf4j
public final class JsonResponseUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    private JsonResponseUtil() {
        // Utility class
    }

    /**
     * Write error response with path
     */
    public static void writeErrorResponse(HttpServletResponse response,
                                        String error,
                                        String message,
                                        int status,
                                        String path) throws IOException {
        Map<String, Object> errorResponse = createErrorMap(error, message, status, path);
        writeJsonResponse(response, errorResponse, status);
    }

    /**
     * Write error response without path
     */
    public static void writeErrorResponse(HttpServletResponse response,
                                        String error,
                                        String message,
                                        int status) throws IOException {
        Map<String, Object> errorResponse = createErrorMap(error, message, status, null);
        writeJsonResponse(response, errorResponse, status);
    }

    /**
     * Write success response
     */
    public static void writeSuccessResponse(HttpServletResponse response,
                                          String message,
                                          Object data) throws IOException {
        Map<String, Object> successResponse = createSuccessMap(message, data);
        writeJsonResponse(response, successResponse, HttpServletResponse.SC_OK);
    }

    /**
     * Write custom JSON response
     */
    public static void writeJsonResponse(HttpServletResponse response,
                                       Object responseObject,
                                       int status) throws IOException {
        response.setStatus(status);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        try {
            String jsonResponse = objectMapper.writeValueAsString(responseObject);
            response.getWriter().write(jsonResponse);
            response.getWriter().flush();
        } catch (Exception e) {
            log.error("Failed to write JSON response", e);
            response.getWriter().write("{\"error\":\"Internal server error\"}");
            response.getWriter().flush();
        }
    }

    /**
     * Create error map
     */
    private static Map<String, Object> createErrorMap(String error, String message, int status, String path) {
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("error", error);
        errorResponse.put("message", message);
        errorResponse.put("status", status);
        errorResponse.put("timestamp", LocalDateTime.now());
        
        if (path != null) {
            errorResponse.put("path", path);
        }
        
        return errorResponse;
    }

    /**
     * Create success map
     */
    private static Map<String, Object> createSuccessMap(String message, Object data) {
        Map<String, Object> successResponse = new HashMap<>();
        successResponse.put("success", true);
        successResponse.put("message", message);
        successResponse.put("timestamp", LocalDateTime.now());
        
        if (data != null) {
            successResponse.put("data", data);
        }
        
        return successResponse;
    }
}
