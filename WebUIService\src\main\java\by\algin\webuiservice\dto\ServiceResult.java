package by.algin.webuiservice.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ServiceResult<T> {
    private boolean success;
    private String errorMessage;
    private String successMessage;
    private T data;

    public static <T> ServiceResult<T> success() {
        return new ServiceResult<>(true, null, null, null);
    }

    public static <T> ServiceResult<T> success(T data) {
        return new ServiceResult<>(true, null, null, data);
    }

    public static <T> ServiceResult<T> success(String successMessage) {
        return new ServiceResult<>(true, null, successMessage, null);
    }

    public static <T> ServiceResult<T> success(T data, String successMessage) {
        return new ServiceResult<>(true, null, successMessage, data);
    }

    public static <T> ServiceResult<T> error(String errorMessage) {
        return new ServiceResult<>(false, errorMessage, null, null);
    }

    public boolean isSuccess() {
        return success;
    }

    public boolean isError() {
        return !success;
    }
}
