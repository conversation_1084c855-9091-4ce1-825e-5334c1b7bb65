package by.algin.projectservice.controller;

import by.algin.constants.CommonPathConstants;
import by.algin.projectservice.constants.ProjectMessageConstants;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@RestController
public class HealthController {

    @GetMapping(CommonPathConstants.HEALTH_ENDPOINT)
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put(ProjectMessageConstants.HEALTH_STATUS_KEY, ProjectMessageConstants.HEALTH_STATUS_UP);
        response.put(ProjectMessageConstants.HEALTH_SERVICE_KEY, ProjectMessageConstants.HEALTH_SERVICE_NAME);
        response.put(ProjectMessageConstants.HEALTH_TIMESTAMP_KEY, LocalDateTime.now());
        response.put(ProjectMessageConstants.HEALTH_VERSION_KEY, ProjectMessageConstants.HEALTH_VERSION);
        return ResponseEntity.ok(response);
    }
}
