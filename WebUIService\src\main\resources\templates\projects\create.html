<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>Create New Project</title>
    <link rel="stylesheet" th:href="@{/css/variables.css}">
    <link rel="stylesheet" th:href="@{/css/base.css}">
    <link rel="stylesheet" th:href="@{/css/forms.css}">
    <link rel="stylesheet" th:href="@{/css/buttons.css}">
    <link rel="stylesheet" th:href="@{/css/alerts.css}">
</head>
<body>
    <div class="language-switcher">
        <a th:href="@{/setLanguage(lang='en')}" th:text="#{language.english}">English</a> |
        <a th:href="@{/setLanguage(lang='ru')}" th:text="#{language.russian}">Russian</a>
    </div>

    <div class="container">
        <h1>Create New Project</h1>

        <div th:if="${error}" class="alert alert-danger">
            <p th:text="${error}"></p>
        </div>
        <form th:action="@{/projects/create}" th:object="${project}" method="post">
            <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}" />

            <div class="form-group">
                <label for="name">Project Name *</label>
                <input type="text" 
                       id="name" 
                       th:field="*{name}" 
                       placeholder="Enter project name"
                       required />
                <div th:if="${#fields.hasErrors('name')}" class="error">
                    <span th:errors="*{name}"></span>
                </div>
            </div>

            <div class="form-group">
                <label for="description">Description</label>
                <textarea id="description" 
                         th:field="*{description}" 
                         placeholder="Enter project description (optional)"></textarea>
                <div th:if="${#fields.hasErrors('description')}" class="error">
                    <span th:errors="*{description}"></span>
                </div>
            </div>

            <div class="form-group">
                <button type="submit" class="btn btn-primary">Create Project</button>
                <a th:href="@{/dashboard}" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</body>
</html>
