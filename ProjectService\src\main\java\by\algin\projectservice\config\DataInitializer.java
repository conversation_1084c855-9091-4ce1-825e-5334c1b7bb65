package by.algin.projectservice.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
@Slf4j
public class DataInitializer implements CommandLineRunner {

    private final RoleInitializer roleInitializer;
    private final PermissionInitializer permissionInitializer;
    private final StatusTransitionInitializer statusTransitionInitializer;

    @Override
    @Transactional
    public void run(String... args) {
        log.info("Starting data initialization for ProjectService");

        roleInitializer.initializeRoles();
        permissionInitializer.initializePermissions();
        statusTransitionInitializer.initializeStatusTransitions();

        log.info("Data initialization completed successfully");
    }
}
