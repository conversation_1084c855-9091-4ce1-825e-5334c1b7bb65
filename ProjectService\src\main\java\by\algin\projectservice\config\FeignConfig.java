package by.algin.projectservice.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

@Configuration
@Slf4j
public class FeignConfig {

    @Bean
    public RequestInterceptor globalRequestInterceptor() {
        return new RequestInterceptor() {
            @Override
            public void apply(RequestTemplate template) {
                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

                if (authentication != null && authentication.isAuthenticated()) {
                    try {
                        Object credentials = authentication.getCredentials();
                        if (credentials instanceof String) {
                            String jwtToken = (String) credentials;
                            if (jwtToken != null && !jwtToken.isEmpty()) {
                                template.header("Authorization", "Bearer " + jwtToken);
                                log.debug("Added JWT token to Feign request");
                            }
                        }
                    } catch (Exception e) {
                        log.warn("Failed to add JWT token to Feign request: {}", e.getMessage());
                    }

                    try {
                        String userId = authentication.getName();
                        template.header("X-User-ID", userId);
                        template.header("X-Requesting-User", userId);
                        log.debug("Added user headers: {}", userId);
                    } catch (Exception e) {
                        log.warn("Failed to add user headers: {}", e.getMessage());
                    }
                }

                template.header("X-Service-Name", "ProjectService");
                template.header("X-Client-Type", "Feign");

                if (!template.headers().containsKey("Content-Type")) {
                    template.header("Content-Type", "application/json");
                }

                if (!template.headers().containsKey("Accept")) {
                    template.header("Accept", "application/json");
                }

                log.debug("Applied global Feign interceptor for: {}", template.url());
            }
        };
    }
}
