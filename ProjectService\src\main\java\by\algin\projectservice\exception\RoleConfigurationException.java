package by.algin.projectservice.exception;

import by.algin.common.exception.ValidationException;

import java.util.Map;

public class RoleConfigurationException extends ValidationException {
    
    private final String configurationSection;
    private final String roleName;
    
    public RoleConfigurationException(String message) {
        super(message);
        this.configurationSection = null;
        this.roleName = null;
    }
    
    public RoleConfigurationException(String message, String configurationSection) {
        super(message);
        this.configurationSection = configurationSection;
        this.roleName = null;
    }
    
    public RoleConfigurationException(String message, String configurationSection, String roleName) {
        super(message);
        this.configurationSection = configurationSection;
        this.roleName = roleName;
    }
    
    public RoleConfigurationException(String message, Map<String, String> errors) {
        super(message, errors);
        this.configurationSection = null;
        this.roleName = null;
    }
    
    public RoleConfigurationException(String message, Throwable cause) {
        super(message, cause);
        this.configurationSection = null;
        this.roleName = null;
    }
    
    public String getConfigurationSection() {
        return configurationSection;
    }
    
    public String getRoleName() {
        return roleName;
    }
}
