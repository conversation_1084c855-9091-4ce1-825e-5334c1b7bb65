package by.algin.projectservice.security;

import by.algin.projectservice.constants.ProjectMessageConstants;
import by.algin.projectservice.exception.JwtAuthenticationException;
import by.algin.projectservice.exception.ProjectErrorCodes;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class JwtAuthenticationService {
    
    private final BearerTokenResolver bearerTokenResolver;
    private final JwtAuthenticationProvider jwtAuthenticationProvider;

    public boolean processAuthentication(HttpServletRequest request) {
        if (SecurityContextHolder.getContext().getAuthentication() != null) {
            log.debug("Request already authenticated, skipping JWT processing");
            return true;
        }

        if (!bearerTokenResolver.hasToken(request)) {
            log.debug("No Bearer token found in request");
            return false;
        }
        
        try {
            String token = bearerTokenResolver.resolve(request);
            UsernamePasswordAuthenticationToken authenticationToken =
                new UsernamePasswordAuthenticationToken(null, token);
            authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
            Authentication authentication = jwtAuthenticationProvider.authenticate(authenticationToken);
            SecurityContextHolder.getContext().setAuthentication(authentication);
            setUserAttributesInRequest(request, authentication);
            log.debug("JWT authentication successful");
            return true;

        } catch (JwtAuthenticationException e) {
            log.warn("JWT authentication failed: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during JWT authentication: {}", e.getMessage(), e);
            throw new JwtAuthenticationException(
                ProjectErrorCodes.JWT_VALIDATION_FAILED,
                "JWT authentication failed due to unexpected error",
                e
            );
        }
    }
    
    private void setUserAttributesInRequest(HttpServletRequest request, Authentication authentication) {
        String principal = authentication.getName();
        if (principal != null) {
            try {
                Long userId = Long.parseLong(principal);
                request.setAttribute("userId", userId);
                log.debug("Set userId attribute: {}", userId);
            } catch (NumberFormatException e) {
                request.setAttribute("username", principal);
                log.debug("Set username attribute: {}", principal);
            }

            log.debug(ProjectMessageConstants.AUTHENTICATION_SET_FOR_USER, principal, principal);
        }
    }
}
