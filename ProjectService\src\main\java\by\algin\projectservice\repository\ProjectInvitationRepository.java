package by.algin.projectservice.repository;

import by.algin.dto.project.InvitationStatus;
import by.algin.projectservice.entity.ProjectInvitation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ProjectInvitationRepository extends JpaRepository<ProjectInvitation, Long> {

    Optional<ProjectInvitation> findByToken(String token);

    Page<ProjectInvitation> findByProjectId(Long projectId, Pageable pageable);

    List<ProjectInvitation> findByInvitedEmailAndStatus(String email, InvitationStatus status);

    Optional<ProjectInvitation> findByProjectIdAndInvitedEmailAndStatus(
            Long projectId, String email, InvitationStatus status);

    boolean existsByProjectIdAndInvitedEmailAndStatus(
            Long projectId, String email, InvitationStatus status);

    long countByProjectIdAndStatus(Long projectId, InvitationStatus status);

    @Query("SELECT i FROM ProjectInvitation i WHERE i.status = :status AND i.expiresAt < :now")
    List<ProjectInvitation> findExpiredInvitations(@Param("status") InvitationStatus status, @Param("now") LocalDateTime now);

    @Modifying
    @Query("UPDATE ProjectInvitation i SET i.status = :newStatus WHERE i.status = :oldStatus AND i.expiresAt < :now")
    int updateExpiredInvitations(@Param("oldStatus") InvitationStatus oldStatus, @Param("newStatus") InvitationStatus newStatus, @Param("now") LocalDateTime now);
}
