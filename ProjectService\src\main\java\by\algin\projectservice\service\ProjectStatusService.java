package by.algin.projectservice.service;

import by.algin.dto.project.ProjectStatus;
import by.algin.projectservice.entity.Project;
import by.algin.projectservice.exception.BusinessRuleViolationException;
import by.algin.projectservice.repository.ProjectRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Set;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ProjectStatusService {

    private final ProjectRepository projectRepository;
    private final ProjectStatusTransitionService transitionService;

    @Transactional
    public void changeProjectStatus(Project project, ProjectStatus newStatus, Long userId) {
        changeProjectStatus(project, newStatus, userId, null);
    }

    @Transactional
    public void changeProjectStatus(Project project, ProjectStatus newStatus, Long userId, String userRole) {
        log.info("Changing project {} status from {} to {} by user {} with role {}",
                project.getId(), project.getStatus(), newStatus, userId, userRole);

        validateStatusTransition(project.getStatus(), newStatus, userRole);

        project.setStatus(newStatus);
        projectRepository.save(project);

        log.info("Project {} status changed to {} successfully", project.getId(), newStatus);
    }

    public void archiveProject(Project project, Long userId) {
        changeProjectStatus(project, ProjectStatus.ARCHIVED, userId);
    }

    public void activateProject(Project project, Long userId) {
        changeProjectStatus(project, ProjectStatus.ACTIVE, userId);
    }

    public void completeProject(Project project, Long userId) {
        changeProjectStatus(project, ProjectStatus.COMPLETED, userId);
    }

    public boolean canModifyProject(Project project) {
        return project.getStatus() == ProjectStatus.ACTIVE;
    }

    public boolean canAddMembers(Project project) {
        return canModifyProject(project);
    }

    public boolean canTransitionTo(Project project, ProjectStatus newStatus) {
        return getValidTransitions(project.getStatus()).contains(newStatus);
    }

    public void validateTransition(ProjectStatus currentStatus, ProjectStatus newStatus) {
        validateStatusTransition(currentStatus, newStatus, null);
    }

    public void validateTransition(ProjectStatus currentStatus, ProjectStatus newStatus, String userRole) {
        validateStatusTransition(currentStatus, newStatus, userRole);
    }

    public Set<ProjectStatus> getValidTransitions(ProjectStatus currentStatus) {
        return transitionService.getValidTransitions(currentStatus);
    }

    private void validateStatusTransition(ProjectStatus currentStatus, ProjectStatus newStatus, String userRole) {
        if (currentStatus == newStatus) {
            throw new BusinessRuleViolationException("Project is already in " + newStatus + " status");
        }

        if (!transitionService.isTransitionAllowed(currentStatus, newStatus, userRole)) {
            String roleInfo = userRole != null ? " for role " + userRole : "";
            throw new BusinessRuleViolationException(
                String.format("Cannot change project status from %s to %s%s", currentStatus, newStatus, roleInfo));
        }
    }
}
