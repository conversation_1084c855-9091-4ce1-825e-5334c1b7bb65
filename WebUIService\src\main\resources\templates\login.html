<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="#{auth.login.title}">Sign In</title>
    <style>
        .error-field {
            border: 1px solid red;
            background-color: #ffe6e6;
        }
        .error-message {
            color: red;
            font-size: 0.9em;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div style="text-align: right; margin: 10px;">
        <a th:href="@{/setLanguage(lang='en')}" th:text="#{language.english}">English</a> |
        <a th:href="@{/setLanguage(lang='ru')}" th:text="#{language.russian}">Russian</a>
    </div>

    <h2 th:text="#{auth.login.title}">Sign In</h2>

    <form th:action="@{/auth/login}" th:object="${loginRequest}" method="post">
        <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}" />
        <div th:if="${loginError}">
            <p class="error-message" th:text="${loginError}"></p>
        </div>
        <div th:if="${error}">
            <p class="error-message" th:text="${error}"></p>
        </div>

        <div>
            <label for="usernameOrEmail" th:text="#{auth.username}">Username or Email:</label>
            <input type="text" id="usernameOrEmail" th:field="*{usernameOrEmail}"
                   th:placeholder="#{auth.email}"
                   th:class="${#fields.hasErrors('usernameOrEmail')} ? 'error-field' : ''"
                   required/>
            <div th:if="${#fields.hasErrors('usernameOrEmail')}">
                <p class="error-message" th:each="error : ${#fields.errors('usernameOrEmail')}" th:text="${error}"></p>
            </div>
        </div>

        <div>
            <label for="password" th:text="#{auth.password}">Password:</label>
            <input type="password" id="password" th:field="*{password}"
                   th:placeholder="#{auth.password}"
                   th:class="${#fields.hasErrors('password')} ? 'error-field' : ''"
                   required/>
            <div th:if="${#fields.hasErrors('password')}">
                <p class="error-message" th:each="error : ${#fields.errors('password')}" th:text="${error}"></p>
            </div>
        </div>

        <div>
            <button type="submit" th:text="#{auth.login.button}">Sign In</button>
        </div>
    </form>

    <div>
        <a th:href="@{/}" th:text="#{nav.home}">Return to homepage</a> |
        <a th:href="@{/auth/register}" th:text="#{auth.register}">Register</a>
    </div>
</body>
</html>