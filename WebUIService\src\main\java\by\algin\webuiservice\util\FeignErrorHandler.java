package by.algin.webuiservice.util;

import by.algin.webuiservice.constants.HttpStatusConstants;
import by.algin.webuiservice.constants.MessageConstants;
import by.algin.webuiservice.constants.ModelAttributeConstants;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;

@Slf4j
@Component
public class FeignErrorHandler {



    private static final String MESSAGE_JSON_KEY = "\"message\":\"";
    private static final int MESSAGE_JSON_KEY_LENGTH = 11;
    private static final int MESSAGE_START_OFFSET = 10;
    private static final int MAX_USER_FRIENDLY_MESSAGE_LENGTH = 200;

    public void handleFeignError(FeignException ex, Model model, String operation, String username) {
        String userMessage = extractUserFriendlyMessage(ex);
        if (model != null) {
            ModelUtils.addError(model, userMessage);
        }

        logFeignError(ex, operation, username);
    }

    public void handleAuthFeignError(FeignException ex, Model model, String operation, String username) {
        String userMessage = extractAuthUserFriendlyMessage(ex);
        if (model != null) {
            ModelUtils.addError(model, userMessage);
        }

        logFeignError(ex, operation, username);
    }

    public void handleServiceError(Exception ex, Model model, String operation, String username) {
        ModelUtils.addError(model, MessageConstants.PROJECT_SERVICE_UNAVAILABLE);
        log.error("Error during {}: for user: {}, error: {}", operation, username, ex.getMessage(), ex);
    }

    public String extractUserFriendlyMessage(FeignException ex) {
        String extractedMessage = extractMessageFromResponse(ex);
        if (StringUtils.hasText(extractedMessage) && isUserFriendlyMessage(extractedMessage)) {
            return extractedMessage;
        }

        return getMessageByHttpStatus(ex.status());
    }

    public String extractAuthUserFriendlyMessage(FeignException ex) {
        String extractedMessage = extractMessageFromResponse(ex);
        if (StringUtils.hasText(extractedMessage) && isUserFriendlyMessage(extractedMessage)) {
            return extractedMessage;
        }

        return getAuthErrorMessage(ex.status());
    }

    public String getMessageByHttpStatus(int status) {
        switch (status) {
            case HttpStatusConstants.HTTP_BAD_REQUEST:
                return MessageConstants.INVALID_PROJECT_DATA;
            case HttpStatusConstants.HTTP_UNAUTHORIZED:
                return MessageConstants.AUTHENTICATION_REQUIRED;
            case HttpStatusConstants.HTTP_FORBIDDEN:
                return MessageConstants.PERMISSION_DENIED_CREATE_PROJECTS;
            case HttpStatusConstants.HTTP_NOT_FOUND:
                return MessageConstants.PROJECT_NOT_FOUND;
            case HttpStatusConstants.HTTP_CONFLICT:
                return MessageConstants.PROJECT_NAME_EXISTS;
            case HttpStatusConstants.HTTP_SERVICE_UNAVAILABLE:
                return MessageConstants.SERVICE_TEMPORARILY_UNAVAILABLE;
            case HttpStatusConstants.HTTP_INTERNAL_SERVER_ERROR:
                return MessageConstants.SERVER_ERROR;
            default:
                return MessageConstants.PROJECT_SERVICE_UNAVAILABLE;
        }
    }

    public String getAuthErrorMessage(int status) {
        switch (status) {
            case HttpStatusConstants.HTTP_BAD_REQUEST:
                return "Invalid registration data. Please check your input.";
            case HttpStatusConstants.HTTP_UNAUTHORIZED:
                return MessageConstants.AUTHENTICATION_REQUIRED;
            case HttpStatusConstants.HTTP_FORBIDDEN:
                return "Access denied. Please check your credentials.";
            case HttpStatusConstants.HTTP_NOT_FOUND:
                return MessageConstants.USER_NOT_FOUND;
            case HttpStatusConstants.HTTP_CONFLICT:
                return "User with this email or username already exists.";
            case HttpStatusConstants.HTTP_INTERNAL_SERVER_ERROR:
                return MessageConstants.REGISTRATION_EMAIL_ISSUE;
            case HttpStatusConstants.HTTP_SERVICE_UNAVAILABLE:
                return MessageConstants.SERVICE_TEMPORARILY_UNAVAILABLE;
            default:
                return "Authentication service is currently unavailable.";
        }
    }
    public boolean isCriticalError(int status) {
        return status >= HttpStatusConstants.SERVER_ERROR_START;
    }

    public boolean isUserError(int status) {
        return status >= HttpStatusConstants.CLIENT_ERROR_START && status < HttpStatusConstants.CLIENT_ERROR_END;
    }

    private String extractMessageFromResponse(FeignException ex) {
        try {
            String responseBody = ex.contentUTF8();
            if (StringUtils.hasText(responseBody) && responseBody.contains("\"message\"")) {
                int messageStart = responseBody.indexOf(MESSAGE_JSON_KEY) + MESSAGE_JSON_KEY_LENGTH;
                int messageEnd = responseBody.indexOf("\"", messageStart);
                if (messageStart > MESSAGE_START_OFFSET && messageEnd > messageStart) {
                    return responseBody.substring(messageStart, messageEnd);
                }
            }
        } catch (Exception e) {
            log.debug("Failed to extract error message from response: {}", e.getMessage());
        }
        return null;
    }

    private boolean isUserFriendlyMessage(String message) {
        if (!StringUtils.hasText(message)) {
            return false;
        }

        String lowerMessage = message.toLowerCase();
        return !lowerMessage.contains("exception") &&
               !lowerMessage.contains("error") &&
               !lowerMessage.contains("null") &&
               !lowerMessage.contains("stack") &&
               message.length() < MAX_USER_FRIENDLY_MESSAGE_LENGTH;
    }

    private void logFeignError(FeignException ex, String operation, String username) {
        if (isCriticalError(ex.status())) {
            log.error("Critical error during {}: for user: {}, HTTP status: {}, error: {}", 
                    operation, username, ex.status(), ex.getMessage());
        } else if (isUserError(ex.status())) {
            log.warn("User error during {}: for user: {}, HTTP status: {}, details: {}", 
                    operation, username, ex.status(), ex.getMessage());
        } else {
            log.info("Service issue during {}: for user: {}, HTTP status: {}", 
                    operation, username, ex.status());
        }
    }
}
