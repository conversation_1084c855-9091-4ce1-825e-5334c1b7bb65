package by.algin.constants;

public final class CommonMessageConstants {

    // Authentication messages
    public static final String LOGIN_SUCCESSFUL = "Login successful";
    public static final String TOKEN_REFRESHED_SUCCESSFULLY = "Token refreshed successfully";
    public static final String TOKEN_IS_VALID = "Token is valid";
    public static final String USER_REGISTERED_SUCCESSFULLY = "User registered successfully. Please check your email for confirmation instructions.";
    public static final String ACCOUNT_CONFIRMED_SUCCESSFULLY = "Account confirmed successfully. You can now login.";
    public static final String CONFIRMATION_EMAIL_RESENT = "Confirmation email has been resent. Please check your email.";

    // Error messages
    public static final String REFRESH_TOKEN_NULL_OR_EMPTY = "Refresh token cannot be null or empty";
    public static final String INVALID_REFRESH_TOKEN_FORMAT = "Invalid refresh token format or signature";
    public static final String TOKEN_NOT_REFRESH_TOKEN = "Token is not a refresh token";
    public static final String INVALID_TOKEN = "Invalid token";
    public static final String USER_IS_DISABLED = "User is disabled";
    public static final String TOKEN_HAS_EXPIRED = "Token has expired";
    public static final String INVALID_TOKEN_SIGNATURE = "Invalid token signature";
    public static final String INVALID_TOKEN_FORMAT = "Invalid token format";
    public static final String UNSUPPORTED_TOKEN_TYPE = "Unsupported token type";
    public static final String TOKEN_VALIDATION_FAILED = "Token validation failed";
    public static final String MISSING_TOKEN = "Missing token";
    public static final String JWT_AUTHENTICATION_FAILED = "JWT authentication failed";

    // Validation error messages
    public static final String EMAIL_NULL_OR_EMPTY = "Email cannot be null or empty";
    public static final String TOKEN_NULL_OR_EMPTY = "Token cannot be null or empty";
    public static final String INVALID_CONFIRMATION_TOKEN = "Invalid confirmation token";
    public static final String USER_EMAIL_NULL = "User email cannot be null";
    public static final String FAILED_TO_SEND_EMAIL = "Failed to send confirmation email: ";

    // Success messages
    public static final String USER_FOUND_SIMPLE = "User found";
    public static final String CONFIRMATION_EMAIL_SENT_TO = "Confirmation email sent successfully to: {}";
    public static final String ACCOUNT_CONFIRMED_FOR_USER = "Account confirmed successfully for user: {}";

    // Log messages
    public static final String PROCESSING_LOGIN_REQUEST = "Processing login request for user: {}";
    public static final String LOGIN_SUCCESSFUL_FOR_USER = "Login successful for user: {}";
    public static final String INVALID_CREDENTIALS_FOR_USER = "Invalid credentials for user: {}";
    public static final String ACCOUNT_DISABLED_FOR_USER = "Account disabled for user: {}";
    public static final String PROCESSING_TOKEN_REFRESH = "Processing token refresh request";
    public static final String TOKEN_REFRESHED_FOR_USER = "Token refreshed successfully for user: {}";
    public static final String PROCESSING_TOKEN_VALIDATION = "Processing token validation request";
    public static final String TOKEN_VALIDATED_FOR_USER = "Token validated successfully for user: {}";

    // Error reasons
    public static final String REASON_BAD_CREDENTIALS = "bad_credentials";
    public static final String REASON_AUTHENTICATION_FAILED = "authentication_failed";
    public static final String REASON_ACCESS_DENIED = "access_denied";
    public static final String REASON_TOKEN_EXPIRED = "token_expired";
    public static final String REASON_USER_NOT_FOUND = "user_not_found";
    public static final String REASON_INVALID_EMAIL_FORMAT = "invalid_email_format";
    public static final String REASON_ACCOUNT_ALREADY_CONFIRMED = "account_already_confirmed";

    // Detail keys
    public static final String DETAIL_KEY_REASON = "reason";
    public static final String DETAIL_KEY_ORIGINAL_MESSAGE = "originalMessage";
    public static final String DETAIL_KEY_EXCEPTION_TYPE = "exceptionType";

    // Search fields
    public static final String SEARCH_FIELD_ID = "id";
    public static final String SEARCH_FIELD_USERNAME = "username";
    public static final String SEARCH_FIELD_EMAIL = "email";

    // Project success messages
    public static final String PROJECT_DELETED_SUCCESSFULLY = "Project deleted successfully";
    public static final String MEMBER_REMOVED_SUCCESSFULLY = "Member removed successfully";
    public static final String INVITATION_ACCEPTED_SUCCESSFULLY = "Invitation accepted successfully";
    public static final String INVITATION_REJECTED_SUCCESSFULLY = "Invitation rejected successfully";
    public static final String INVITATION_CANCELED_SUCCESSFULLY = "Invitation canceled successfully";
    public static final String CONFIGURATION_VALIDATION_COMPLETED = "Configuration validation completed successfully. Check logs for details.";

    // Project error messages
    public static final String CONFIGURATION_VALIDATION_FAILED = "Configuration validation failed";
    public static final String CONFIGURATION_VALIDATION_FAILED_WITH_MESSAGE = "Configuration validation failed: ";
    public static final String TOKEN_VALIDATION_FAILED_UNAUTHORIZED = "Token validation failed - unauthorized";
    public static final String USER_SERVICE_TOKEN_VALIDATION_FAILED = "UserService token validation failed, falling back to local validation: ";
    public static final String JWT_TOKEN_VALIDATION_FAILED_FOR_USER = "JWT token validation failed for user: {} (local: {}, remote: {})";
    public static final String JWT_TOKEN_VALIDATION_FAILED = "JWT token validation failed: ";

    // Additional error messages for Feign
    public static final String EMAIL_ALREADY_EXISTS = "Email already exists";
    public static final String USERNAME_ALREADY_EXISTS = "Username already exists";
    public static final String CONFLICT = "Conflict occurred";
    public static final String AUTH_SERVICE_UNAVAILABLE = "Authentication service is unavailable";
    public static final String AUTHENTICATION_FAILED = "Authentication failed";
    public static final String EXTERNAL_SERVICE_ERROR = "External service error";
    public static final String INVALID_CREDENTIALS = "Invalid credentials";
    public static final String ACCESS_DENIED = "Access denied";
    public static final String USER_NOT_FOUND = "User not found";
    public static final String RESOURCE_NOT_FOUND = "Resource not found";
    public static final String INTERNAL_SERVER_ERROR = "Internal server error";
    public static final String SERVICE_UNAVAILABLE = "Service unavailable";
    public static final String INVALID_INPUT = "Invalid input";
    public static final String AUTHENTICATION_REQUIRED = "Authentication required";

    private CommonMessageConstants() {
    }
}
