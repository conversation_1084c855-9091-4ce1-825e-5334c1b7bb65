package by.algin.common.dto;

import by.algin.common.exception.ApiErrorCode;
import org.springframework.http.HttpStatus;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class ErrorDetailsDTOBuilder {
    
    private final ErrorDetailsDTO.ErrorDetailsDTOBuilder builder;
    
    private ErrorDetailsDTOBuilder() {
        this.builder = ErrorDetailsDTO.builder();
    }
    
    public static ErrorDetailsDTOBuilder create() {
        return new ErrorDetailsDTOBuilder();
    }
    
    public static ErrorDetailsDTOBuilder fromApiErrorCode(ApiErrorCode errorCode) {
        return new ErrorDetailsDTOBuilder()
                .errorCode(errorCode.getCode())
                .message(errorCode.getDefaultMessage())
                .status(errorCode.getHttpStatus())
                .category(errorCode.getCategory());
    }

    public ErrorDetailsDTOBuilder errorCode(String errorCode) {
        builder.errorCode(errorCode);
        return this;
    }

    public ErrorDetailsDTOBuilder message(String message) {
        builder.message(message);
        return this;
    }
    
    public ErrorDetailsDTOBuilder status(HttpStatus httpStatus) {
        builder.status(httpStatus.value());
        return this;
    }
    
    public ErrorDetailsDTOBuilder status(int statusCode) {
        builder.status(statusCode);
        return this;
    }
    
    public ErrorDetailsDTOBuilder category(String category) {
        builder.category(category);
        return this;
    }

    public ErrorDetailsDTOBuilder path(String path) {
        builder.path(path);
        return this;
    }

    public ErrorDetailsDTOBuilder serviceName(String serviceName) {
        builder.serviceName(serviceName);
        return this;
    }
    
    public ErrorDetailsDTOBuilder reasonKey(String reasonKey) {
        builder.reasonKey(reasonKey);
        return this;
    }
    
    public ErrorDetailsDTOBuilder exceptionType(Exception ex) {
        if (ex != null) {
            builder.exceptionType(ex.getClass().getSimpleName());
        }
        return this;
    }

    public ErrorDetailsDTOBuilder originalMessage(Exception ex) {
        if (ex != null && ex.getMessage() != null) {
            builder.originalMessage(ex.getMessage());
        }
        return this;
    }
    
    public ErrorDetailsDTOBuilder addDetail(String key, Object value) {
        Map<String, Object> details = builder.build().getAdditionalDetails();
        if (details == null) {
            details = new HashMap<>();
            builder.additionalDetails(details);
        }
        details.put(key, value);
        return this;
    }

    public ErrorDetailsDTOBuilder additionalDetails(Map<String, Object> details) {
        builder.additionalDetails(details);
        return this;
    }
    
    public ErrorDetailsDTOBuilder generateTraceId() {
        builder.traceId(UUID.randomUUID().toString());
        return this;
    }

    public ErrorDetailsDTOBuilder traceId(String traceId) {
        builder.traceId(traceId);
        return this;
    }

    public ErrorDetailsDTO build() {
        return builder.build();
    }
}
