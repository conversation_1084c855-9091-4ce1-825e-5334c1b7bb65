package by.algin.projectservice.service;

import by.algin.dto.project.ProjectStatus;
import by.algin.projectservice.exception.BusinessRuleViolationException;
import by.algin.projectservice.util.ValidationUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class StatusTransitionValidationService {

    private final ValidationUtils validationUtils;
    private final ProjectStatusTransitionService transitionService;

    public void validateTransitionInput(ProjectStatus fromStatus, ProjectStatus toStatus, String userRole) {
        log.debug("Validating transition input: {} -> {}, userRole: {}", fromStatus, toStatus, userRole);
        
        validationUtils.validateStatusTransition(fromStatus, toStatus);
        validationUtils.validateUserRole(userRole);
        
        log.debug("Transition input validation passed");
    }

    public void validateTransitionAllowed(ProjectStatus fromStatus, ProjectStatus toStatus, String userRole) {
        log.debug("Validating transition allowed: {} -> {}, userRole: {}", fromStatus, toStatus, userRole);
        
        if (!transitionService.isTransitionAllowed(fromStatus, toStatus, userRole)) {
            String roleInfo = userRole != null ? " for role " + userRole : "";
            String message = String.format("Transition from %s to %s is not allowed%s", 
                fromStatus, toStatus, roleInfo);
            
            log.warn("Transition validation failed: {}", message);
            throw new BusinessRuleViolationException(message);
        }
        
        log.debug("Transition validation passed");
    }

    public void validateStatus(ProjectStatus status, String fieldName) {
        validationUtils.validateProjectStatus(status, fieldName);
    }

    public void validateStatusHasTransitions(ProjectStatus status) {
        log.debug("Validating status has transitions: {}", status);
        
        var transitions = transitionService.getValidTransitions(status);
        if (transitions.isEmpty()) {
            String message = String.format("Status %s has no valid transitions configured", status);
            log.warn("Status validation failed: {}", message);
            throw new BusinessRuleViolationException(message);
        }
        
        log.debug("Status has {} valid transitions", transitions.size());
    }

    public void validateCompleteTransition(ProjectStatus fromStatus, ProjectStatus toStatus, String userRole) {
        validateTransitionInput(fromStatus, toStatus, userRole);
        validateTransitionAllowed(fromStatus, toStatus, userRole);
    }
}
