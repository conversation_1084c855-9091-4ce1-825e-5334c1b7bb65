package by.algin.projectservice.repository;

import by.algin.projectservice.entity.ProjectRolePermission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProjectRolePermissionRepository extends JpaRepository<ProjectRolePermission, Long> {

    List<ProjectRolePermission> findByRoleIdOrderByPermissionNameAsc(Long roleId);

    List<ProjectRolePermission> findByRoleIdAndIsGrantedTrueOrderByPermissionNameAsc(Long roleId);

    Optional<ProjectRolePermission> findByRoleIdAndPermissionName(Long roleId, String permissionName);

    @Query("SELECT COUNT(p) > 0 FROM ProjectRolePermission p WHERE p.role.id = :roleId " +
           "AND p.permissionName = :permissionName AND p.isGranted = true")
    boolean hasPermission(@Param("roleId") Long roleId, @Param("permissionName") String permissionName);

    @Query("SELECT p FROM ProjectRolePermission p WHERE p.permissionName = :permissionName " +
           "AND p.isGranted = true AND p.role.isActive = true")
    List<ProjectRolePermission> findRolesWithPermission(@Param("permissionName") String permissionName);

    @Query("SELECT DISTINCT p.permissionName FROM ProjectRolePermission p ORDER BY p.permissionName")
    List<String> findAllDistinctPermissionNames();

    @Query("SELECT p FROM ProjectRolePermission p WHERE p.role.roleName = :roleName " +
           "AND p.role.isActive = true ORDER BY p.permissionName")
    List<ProjectRolePermission> findByRoleName(@Param("roleName") String roleName);

    @Query("SELECT p FROM ProjectRolePermission p WHERE p.role.roleName = :roleName " +
           "AND p.role.isActive = true AND p.isGranted = true ORDER BY p.permissionName")
    List<ProjectRolePermission> findGrantedPermissionsByRoleName(@Param("roleName") String roleName);

    @Query("SELECT COUNT(p) > 0 FROM ProjectRolePermission p WHERE p.role.roleName = :roleName " +
           "AND p.permissionName = :permissionName AND p.isGranted = true AND p.role.isActive = true")
    boolean hasPermissionByRoleName(@Param("roleName") String roleName, @Param("permissionName") String permissionName);

    long countByRoleId(Long roleId);

    long countByRoleIdAndIsGrantedTrue(Long roleId);

    void deleteByRoleId(Long roleId);

    List<ProjectRolePermission> findByCreatedByOrderByCreatedAtDesc(String createdBy);
}
