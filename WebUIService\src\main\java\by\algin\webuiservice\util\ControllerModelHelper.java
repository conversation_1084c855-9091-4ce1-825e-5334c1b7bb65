package by.algin.webuiservice.util;

import by.algin.dto.project.ProjectResponse;
import by.algin.dto.response.UserResponse;
import by.algin.dto.project.ProjectMemberListResponse;
import by.algin.webuiservice.dto.ServiceResult;
import lombok.experimental.UtilityClass;
import org.springframework.ui.Model;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

@UtilityClass
public class ControllerModelHelper {

    public static void prepareProjectViewData(Model model, ProjectResponse project, 
                                            ProjectMemberListResponse members, UserResponse user) {
        if (project != null) {
            ModelUtils.addProject(model, project);
        }
        if (members != null && members.getMembers() != null) {
            ModelUtils.addMembers(model, members.getMembers());
        }
        if (user != null) {
            ModelUtils.addUser(model, user);
        }
    }

    public static void handleServiceResult(ServiceResult result, RedirectAttributes redirectAttributes) {
        if (result.isSuccess()) {
            if (result.getSuccessMessage() != null) {
                redirectAttributes.addFlashAttribute("success", result.getSuccessMessage());
            }
        } else {
            if (result.getErrorMessage() != null) {
                redirectAttributes.addFlashAttribute("error", result.getErrorMessage());
            }
        }
    }

    public static void handleServiceResult(ServiceResult result, Model model) {
        if (result.isSuccess()) {
            if (result.getSuccessMessage() != null) {
                ModelUtils.addSuccess(model, result.getSuccessMessage());
            }
        } else {
            if (result.getErrorMessage() != null) {
                ModelUtils.addError(model, result.getErrorMessage());
            }
        }
    }

    public static void prepareDashboardData(Model model, Object projects, UserResponse user) {
        if (projects != null) {
            ModelUtils.addProjects(model, projects);
        }
        if (user != null) {
            ModelUtils.addUser(model, user);
        }
    }

    public static void addErrorIfNotPresent(Model model, String defaultMessage) {
        if (!ModelUtils.hasError(model)) {
            ModelUtils.addError(model, defaultMessage);
        }
    }

    public static void addSuccessIfNotPresent(Model model, String defaultMessage) {
        if (!ModelUtils.hasSuccess(model)) {
            ModelUtils.addSuccess(model, defaultMessage);
        }
    }
}
