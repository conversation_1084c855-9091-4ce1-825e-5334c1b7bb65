package by.algin.projectservice.service;

import by.algin.projectservice.config.AppProperties;
import by.algin.projectservice.config.RoleConfiguration;
import by.algin.projectservice.entity.ProjectRole;
import by.algin.projectservice.entity.ProjectRolePermission;
import by.algin.projectservice.exception.RoleConfigurationException;
import by.algin.projectservice.exception.RoleNotFoundException;
import by.algin.projectservice.repository.ProjectRoleRepository;
import by.algin.projectservice.repository.ProjectRolePermissionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class RoleConfigurationService {

    private final AppProperties appProperties;
    private final ProjectRoleRepository roleRepository;
    private final ProjectRolePermissionRepository permissionRepository;
    
    private volatile Map<String, RoleConfiguration.RoleDefinition> currentRoleDefinitions = new HashMap<>();
    private volatile Map<String, RoleConfiguration.PermissionDefinition> currentPermissionDefinitions = new HashMap<>();
    private volatile Map<String, List<String>> currentPermissionGroups = new HashMap<>();

    @EventListener(ApplicationReadyEvent.class)
    @Transactional
    public void initializeFromConfiguration() {
        log.info("Initializing roles from configuration");
        reloadConfiguration();
    }

    @Transactional
    public void reloadConfiguration() {
        log.info("Reloading role configuration");

        try {
            RoleConfiguration config = appProperties.getRoleConfiguration();

            validateConfiguration(config);

            this.currentRoleDefinitions = new HashMap<>(config.getDefinitions());
            this.currentPermissionDefinitions = new HashMap<>(config.getPermissions());
            this.currentPermissionGroups = new HashMap<>(config.getPermissionGroups());

            syncRolesToDatabase();
            syncPermissionsToDatabase();

            log.info("Role configuration reloaded successfully. Roles: {}, Permissions: {}",
                    currentRoleDefinitions.size(), currentPermissionDefinitions.size());
        } catch (Exception e) {
            log.error("Failed to reload role configuration", e);
            throw new RoleConfigurationException("Failed to reload role configuration: " + e.getMessage(), e);
        }
    }

    private void syncRolesToDatabase() {
        for (Map.Entry<String, RoleConfiguration.RoleDefinition> entry : currentRoleDefinitions.entrySet()) {
            String roleName = entry.getKey();
            RoleConfiguration.RoleDefinition definition = entry.getValue();
            
            Optional<ProjectRole> existingRole = roleRepository.findByRoleNameIgnoreCase(roleName);
            
            if (existingRole.isPresent()) {
                updateExistingRole(existingRole.get(), definition);
            } else {
                createNewRole(roleName, definition);
            }
        }
    }

    private void updateExistingRole(ProjectRole role, RoleConfiguration.RoleDefinition definition) {
        role.setDisplayName(definition.getDisplayName());
        role.setDescription(definition.getDescription());
        role.setPermissionLevel(definition.getPermissionLevel());
        role.setColorCode(definition.getColor());
        role.setIconName(definition.getIcon());
        role.setMaxPerProject(definition.getMaxPerProject());
        role.setIsActive(definition.getIsActive());
        role.setCanBeAssigned(definition.getCanBeAssigned());
        role.setUpdatedBy("CONFIGURATION_RELOAD");
        
        roleRepository.save(role);
        log.debug("Updated role: {}", role.getRoleName());
    }

    private void createNewRole(String roleName, RoleConfiguration.RoleDefinition definition) {
        ProjectRole role = ProjectRole.builder()
                .roleName(roleName)
                .displayName(definition.getDisplayName())
                .description(definition.getDescription())
                .permissionLevel(definition.getPermissionLevel())
                .colorCode(definition.getColor())
                .iconName(definition.getIcon())
                .maxPerProject(definition.getMaxPerProject())
                .isActive(definition.getIsActive())
                .canBeAssigned(definition.getCanBeAssigned())
                .isSystemRole(definition.getIsSystemRole())
                .createdBy("CONFIGURATION_RELOAD")
                .build();
        
        roleRepository.save(role);
        log.info("Created new role from configuration: {}", roleName);
    }

    private void syncPermissionsToDatabase() {
        for (Map.Entry<String, RoleConfiguration.RoleDefinition> entry : currentRoleDefinitions.entrySet()) {
            String roleName = entry.getKey();
            RoleConfiguration.RoleDefinition definition = entry.getValue();
            
            Optional<ProjectRole> role = roleRepository.findByRoleNameIgnoreCase(roleName);
            if (role.isPresent()) {
                syncRolePermissions(role.get(), definition);
            }
        }
    }

    private void syncRolePermissions(ProjectRole role, RoleConfiguration.RoleDefinition definition) {
        Set<String> allPermissions = new HashSet<>(definition.getPermissions());

        for (String groupName : definition.getPermissionGroups()) {
            List<String> groupPermissions = currentPermissionGroups.get(groupName);
            if (groupPermissions != null) {
                allPermissions.addAll(groupPermissions);
            }
        }
        
        List<ProjectRolePermission> existingPermissions =
                permissionRepository.findGrantedPermissionsByRoleName(role.getRoleName());
        
        Set<String> existingPermissionNames = existingPermissions.stream()
                .map(ProjectRolePermission::getPermissionName)
                .collect(Collectors.toSet());
        
        for (ProjectRolePermission permission : existingPermissions) {
            if (!allPermissions.contains(permission.getPermissionName())) {
                permissionRepository.delete(permission);
                log.debug("Removed permission {} from role {}", 
                        permission.getPermissionName(), role.getRoleName());
            }
        }
        
        for (String permissionName : allPermissions) {
            if (!existingPermissionNames.contains(permissionName)) {
                ProjectRolePermission permission = ProjectRolePermission.builder()
                        .role(role)
                        .permissionName(permissionName)
                        .isGranted(true)
                        .createdBy("CONFIGURATION_RELOAD")
                        .build();
                
                permissionRepository.save(permission);
                log.debug("Added permission {} to role {}", permissionName, role.getRoleName());
            }
        }
    }
    public Set<String> getAvailableRoles() {
        return new HashSet<>(currentRoleDefinitions.keySet());
    }

    public Set<String> getRolePermissions(String roleName) {
        RoleConfiguration.RoleDefinition definition = currentRoleDefinitions.get(roleName);
        if (definition == null) {
            return Set.of();
        }
        
        Set<String> allPermissions = new HashSet<>(definition.getPermissions());
        
        for (String groupName : definition.getPermissionGroups()) {
            List<String> groupPermissions = currentPermissionGroups.get(groupName);
            if (groupPermissions != null) {
                allPermissions.addAll(groupPermissions);
            }
        }
        
        return allPermissions;
    }

    public boolean hasPermission(String roleName, String permissionName) {
        return getRolePermissions(roleName).contains(permissionName);
    }



    private void validateConfiguration(RoleConfiguration config) {

        Map<String, String> errors = new HashMap<>();
        for (Map.Entry<String, RoleConfiguration.RoleDefinition> entry : config.getDefinitions().entrySet()) {
            String roleName = entry.getKey();
            RoleConfiguration.RoleDefinition definition = entry.getValue();

            if (definition.getDisplayName() == null || definition.getDisplayName().trim().isEmpty()) {
                errors.put("role." + roleName + ".displayName", "Display name is required");
            }

            if (definition.getPermissionLevel() == null || definition.getPermissionLevel() < 1 || definition.getPermissionLevel() > 100) {
                errors.put("role." + roleName + ".permissionLevel", "Permission level must be between 1 and 100");
            }

            for (String groupName : definition.getPermissionGroups()) {
                if (!config.getPermissionGroups().containsKey(groupName)) {
                    errors.put("role." + roleName + ".permissionGroups", "Permission group '" + groupName + "' does not exist");
                }
            }
        }

        validatePermissionGroups(config.getPermissionGroups(), errors);

        if (!errors.isEmpty()) {
            throw new RoleConfigurationException("Configuration validation failed", errors);
        }
    }

    private void validatePermissionGroups(Map<String, List<String>> groups, Map<String, String> errors) {
        for (String groupName : groups.keySet()) {
            Set<String> visited = new HashSet<>();
            if (hasCircularDependency(groupName, groups, visited)) {
                errors.put("permissionGroups." + groupName, "Circular dependency detected");
            }
        }
    }

    private boolean hasCircularDependency(String groupName, Map<String, List<String>> groups, Set<String> visited) {
        if (visited.contains(groupName)) {
            return true;
        }

        visited.add(groupName);
        List<String> permissions = groups.get(groupName);

        if (permissions != null) {
            for (String permission : permissions) {
                if (groups.containsKey(permission)) {
                    if (hasCircularDependency(permission, groups, new HashSet<>(visited))) {
                        return true;
                    }
                }
            }
        }

        return false;
    }
}
