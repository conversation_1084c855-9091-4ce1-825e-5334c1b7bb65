.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-inline {
    display: flex;
    gap: var(--spacing-sm);
    align-items: end;
}

.form-inline .form-group {
    flex: 1;
    margin-bottom: 0;
}

label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: bold;
    color: var(--text-primary);
}

input[type="text"],
input[type="email"],
input[type="password"],
textarea,
select {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-md);
    box-sizing: border-box;
}

textarea {
    height: var(--input-height);
    resize: vertical;
}

.add-member-form {
    background-color: var(--bg-light);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color-light);
}

.current-value {
    background-color: var(--bg-light);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-sm);
    border-left: 4px solid var(--border-accent);
}

.current-label {
    font-weight: bold;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.current-text {
    color: var(--text-muted);
}

.error {
    color: var(--color-danger);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
}
