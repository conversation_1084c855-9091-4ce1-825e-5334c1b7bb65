package by.algin.projectservice.config;

import by.algin.dto.project.ProjectStatus;
import by.algin.projectservice.entity.ProjectStatusTransition;
import by.algin.projectservice.repository.ProjectStatusTransitionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
public class StatusTransitionInitializer {

    private final ProjectStatusTransitionRepository statusTransitionRepository;

    public void initializeStatusTransitions() {
        log.info("Initializing project status transitions");

        createActiveTransitions();
        createArchivedTransitions();
        createCompletedTransitions();
        createInactiveTransitions();

        log.info("Project status transitions initialization completed");
    }

    private void createActiveTransitions() {
        createStatusTransitionIfNotExists(
            ProjectStatus.ACTIVE,
            ProjectStatus.ARCHIVED,
            "Archive an active project"
        );
        createStatusTransitionIfNotExists(
            ProjectStatus.ACTIVE,
            ProjectStatus.COMPLETED,
            "Mark active project as completed"
        );
        createStatusTransitionIfNotExists(
            ProjectStatus.ACTIVE,
            ProjectStatus.INACTIVE,
            "Deactivate an active project"
        );
    }

    private void createArchivedTransitions() {
        createStatusTransitionIfNotExists(
            ProjectStatus.ARCHIVED,
            ProjectStatus.ACTIVE,
            "Reactivate an archived project"
        );
    }

    private void createCompletedTransitions() {
        createStatusTransitionIfNotExists(
            ProjectStatus.COMPLETED,
            ProjectStatus.ARCHIVED,
            "Archive a completed project"
        );
    }

    private void createInactiveTransitions() {
        createStatusTransitionIfNotExists(
            ProjectStatus.INACTIVE,
            ProjectStatus.ACTIVE,
            "Reactivate an inactive project"
        );
        createStatusTransitionIfNotExists(
            ProjectStatus.INACTIVE,
            ProjectStatus.ARCHIVED,
            "Archive an inactive project"
        );
    }

    private void createStatusTransitionIfNotExists(ProjectStatus fromStatus, ProjectStatus toStatus, String description) {
        Optional<ProjectStatusTransition> existingTransition =
            statusTransitionRepository.findByFromStatusAndToStatus(fromStatus, toStatus);

        if (existingTransition.isEmpty()) {
            ProjectStatusTransition transition = ProjectStatusTransition.builder()
                .fromStatus(fromStatus)
                .toStatus(toStatus)
                .description(description)
                .createdBy("SYSTEM")
                .build();

            statusTransitionRepository.save(transition);
            log.debug("Created status transition: {} -> {}", fromStatus, toStatus);
        } else {
            log.debug("Status transition {} -> {} already exists, skipping creation", fromStatus, toStatus);
        }
    }
}
