package by.algin.common.exception;

import java.util.Map;

public class ValidationException extends RuntimeException {
    
    private Map<String, String> errors;
    
    public ValidationException() {
        super(CommonErrorCodes.VALIDATION_FAILED.getDefaultMessage());
    }

    public ValidationException(String message) {
        super(message);
    }

    public ValidationException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public ValidationException(String message, Map<String, String> errors) {
        super(message);
        this.errors = errors;
    }
    
    public Map<String, String> getErrors() {
        return errors;
    }
    
    public void setErrors(Map<String, String> errors) {
        this.errors = errors;
    }
}
