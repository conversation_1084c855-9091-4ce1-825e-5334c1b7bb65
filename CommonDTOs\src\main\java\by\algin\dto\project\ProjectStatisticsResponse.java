package by.algin.dto.project;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProjectStatisticsResponse {

    private Long totalProjects;
    private Long activeProjects;
    private Long inactiveProjects;
    private Long completedProjects;
    private Long archivedProjects;
    
    private Long totalMembers;
    private Long ownedProjects;
    private Long managedProjects;
    
    private Map<ProjectStatus, Long> projectsByStatus;
    private Map<ProjectRole, Long> membersByRole;
}
