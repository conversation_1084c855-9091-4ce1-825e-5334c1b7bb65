package by.algin.userservice.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.time.Duration;
import java.util.List;

/**
 * Cache configuration for UserService
 */
@Configuration
@EnableCaching
public class CacheConfig {

    /**
     * Default cache manager for general caching
     */
    @Bean
    @Primary
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        
        // Configure default cache settings
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(Duration.ofMinutes(15))
                .recordStats());
        
        // Define cache names
        cacheManager.setCacheNames(List.of("users", "userSearch", "userExists"));
        
        return cacheManager;
    }

    /**
     * Cache manager for batch operations (larger capacity, longer TTL)
     */
    @Bean("batchCacheManager")
    public CacheManager batchCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager("userBatch", "userExistsBatch");

        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(500) // Smaller size for batch operations
                .expireAfterWrite(Duration.ofMinutes(30)) // Longer TTL for batch data
                .expireAfterAccess(Duration.ofMinutes(10)) // Remove unused entries
                .recordStats());

        return cacheManager;
    }

    /**
     * Cache manager for frequently accessed data (high capacity, short TTL)
     */
    @Bean("frequentCacheManager")
    public CacheManager frequentCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager("userFrequent", "authCache");

        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(2000) // Higher capacity for frequent access
                .expireAfterWrite(Duration.ofMinutes(5)) // Short TTL for fresh data
                .expireAfterAccess(Duration.ofMinutes(2)) // Quick eviction of unused entries
                .recordStats());

        return cacheManager;
    }

    /**
     * Cache manager for long-term data (user profiles, settings)
     */
    @Bean("longTermCacheManager")
    public CacheManager longTermCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager("userProfiles", "userSettings");

        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(1500)
                .expireAfterWrite(Duration.ofHours(2)) // Long TTL for stable data
                .expireAfterAccess(Duration.ofMinutes(30)) // Keep accessed data longer
                .recordStats());

        return cacheManager;
    }
}
