body {
    font-family: Arial, sans-serif;
    margin: 0 auto;
    padding: var(--spacing-lg);
    background-color: var(--bg-secondary);
}

.container {
    background-color: var(--bg-primary);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-light);
    max-width: var(--container-max-width);
    margin: 0 auto;
}

.container-wide {
    max-width: var(--container-max-width-wide);
}

.language-switcher {
    text-align: right;
    margin-bottom: var(--spacing-lg);
}

.language-switcher a {
    color: var(--color-primary);
    text-decoration: none;
}

.language-switcher a:hover {
    text-decoration: underline;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.mb-20 {
    margin-bottom: var(--spacing-lg);
}

.mt-30 {
    margin-top: var(--spacing-xl);
}

.no-margin {
    margin: 0;
}

.text-muted {
    color: var(--text-muted);
}

.font-bold {
    font-weight: bold;
}

.empty-state {
    text-align: center;
    padding: var(--spacing-xxl);
    background-color: var(--bg-light);
    border-radius: var(--border-radius-md);
    color: var(--text-light);
}

.empty-state h4 {
    color: var(--text-light);
    margin-bottom: var(--spacing-sm);
}

.empty-state p {
    color: var(--text-lighter);
    margin: 0;
}
