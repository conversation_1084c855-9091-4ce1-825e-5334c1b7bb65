package by.algin.projectservice.exception;

import lombok.Getter;

@Getter
public class ProjectAccessDeniedException extends RuntimeException {
    private final Long userId;
    private final Long projectId;

    public ProjectAccessDeniedException(Long userId, Long projectId) {
        super("User " + userId + " doesn't have access to project " + projectId);
        this.userId = userId;
        this.projectId = projectId;
    }
}
