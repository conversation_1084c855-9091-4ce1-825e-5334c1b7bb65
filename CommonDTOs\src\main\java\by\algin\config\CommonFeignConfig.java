package by.algin.config;

import by.algin.constants.CommonPathConstants;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * Common Feign configuration used across all services
 * Provides JWT token propagation and common interceptors
 */
@Slf4j
@Configuration
public class CommonFeignConfig {

    private static final List<String> PUBLIC_ENDPOINTS = List.of(
            CommonPathConstants.LOGIN_ENDPOINT,
            CommonPathConstants.REGISTER_ENDPOINT,
            CommonPathConstants.CONFIRM_ENDPOINT,
            CommonPathConstants.RESEND_CONFIRMATION_ENDPOINT,
            CommonPathConstants.EMAIL_BY_TOKEN_ENDPOINT
    );

    /**
     * JWT token interceptor for Feign requests
     * Automatically adds JWT token to outgoing requests
     */
    @Bean
    public RequestInterceptor jwtTokenInterceptor() {
        return new JwtTokenInterceptor();
    }

    /**
     * JWT Token Interceptor implementation
     */
    private static class JwtTokenInterceptor implements RequestInterceptor {

        @Override
        public void apply(RequestTemplate template) {
            // Skip token for public endpoints
            if (isPublicEndpoint(template.url())) {
                log.debug("Skipping JWT token for public endpoint: {}", template.url());
                return;
            }

            String token = extractJwtToken();
            if (token != null) {
                template.header(CommonPathConstants.AUTHORIZATION_HEADER, 
                              CommonPathConstants.BEARER_PREFIX + token);
                log.debug("Added JWT token to Feign request for URL: {}", template.url());
            } else {
                log.debug("No JWT token found for Feign request to: {}", template.url());
            }
        }

        /**
         * Extract JWT token from current request context
         */
        private String extractJwtToken() {
            // Try to get token from Security Context first
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getCredentials() instanceof String) {
                String token = (String) authentication.getCredentials();
                if (token != null && !token.isEmpty()) {
                    return token;
                }
            }

            // Fallback to extracting from HTTP request
            ServletRequestAttributes attributes = 
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                
                // Try Authorization header first
                String authHeader = request.getHeader(CommonPathConstants.AUTHORIZATION_HEADER);
                if (authHeader != null && authHeader.startsWith(CommonPathConstants.BEARER_PREFIX)) {
                    return authHeader.substring(CommonPathConstants.BEARER_PREFIX.length());
                }
                
                // Try JWT cookie as fallback
                Cookie[] cookies = request.getCookies();
                if (cookies != null) {
                    return Arrays.stream(cookies)
                            .filter(cookie -> CommonPathConstants.JWT_COOKIE_NAME.equals(cookie.getName()))
                            .map(Cookie::getValue)
                            .findFirst()
                            .orElse(null);
                }
            }

            return null;
        }

        /**
         * Check if endpoint is public (doesn't require authentication)
         */
        private boolean isPublicEndpoint(String url) {
            return PUBLIC_ENDPOINTS.stream().anyMatch(url::contains);
        }
    }
}
