package by.algin.userservice.controller;

import by.algin.dto.response.ApiResponse;
import by.algin.dto.response.UserResponse;
import by.algin.userservice.service.OptimizedUserService;
import by.algin.userservice.exception.BatchSizeLimitExceededException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.List;
import java.util.Map;

/**
 * Optimized User Controller with enhanced batch operations
 */
@Slf4j
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
@Tag(name = "Optimized User Operations", description = "Enhanced user operations with batch processing and caching")
public class OptimizedUserController {

    private final OptimizedUserService optimizedUserService;

    /**
     * Get multiple users by IDs with size limits and optimization
     */
    @GetMapping("/batch")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Get users by IDs", description = "Retrieve multiple users by their IDs with size limits")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Users retrieved successfully"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Batch size limit exceeded"),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    public ResponseEntity<ApiResponse<List<UserResponse>>> getUsersByIds(
            @Parameter(description = "List of user IDs (max 100)", required = true)
            @RequestParam("ids") List<Long> userIds) {
        
        try {
            log.info("Batch request for {} user IDs", userIds != null ? userIds.size() : 0);
            
            List<UserResponse> users = optimizedUserService.getUsersByIds(userIds);
            
            return ResponseEntity.ok(ApiResponse.success(
                String.format("Retrieved %d users", users.size()), 
                users
            ));
            
        } catch (BatchSizeLimitExceededException e) {
            log.warn("Batch size limit exceeded: {}", e.getMessage());
            return ResponseEntity.badRequest().body(
                ApiResponse.error("BATCH_SIZE_LIMIT_EXCEEDED", e.getMessage())
            );
        } catch (Exception e) {
            log.error("Error processing batch request: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                ApiResponse.error("BATCH_PROCESSING_ERROR", "Failed to process batch request")
            );
        }
    }

    /**
     * Get users by IDs with chunking for large requests
     */
    @GetMapping("/batch/chunked")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Get users by IDs with chunking", description = "Retrieve users in chunks for large requests")
    public ResponseEntity<ApiResponse<List<UserResponse>>> getUsersByIdsChunked(
            @Parameter(description = "List of user IDs", required = true)
            @RequestParam("ids") List<Long> userIds,
            @Parameter(description = "Chunk size (default: 50, max: 100)")
            @RequestParam(value = "chunkSize", defaultValue = "50") 
            @Min(1) @Max(100) int chunkSize) {
        
        try {
            log.info("Chunked batch request for {} user IDs with chunk size {}", 
                    userIds != null ? userIds.size() : 0, chunkSize);
            
            List<UserResponse> users = optimizedUserService.getUsersByIdsChunked(userIds, chunkSize);
            
            return ResponseEntity.ok(ApiResponse.success(
                String.format("Retrieved %d users in chunks", users.size()), 
                users
            ));
            
        } catch (Exception e) {
            log.error("Error processing chunked batch request: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                ApiResponse.error("CHUNKED_BATCH_ERROR", "Failed to process chunked batch request")
            );
        }
    }

    /**
     * Get users by IDs with caching
     */
    @GetMapping("/batch/cached")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Get users by IDs with caching", description = "Retrieve users with caching for better performance")
    public ResponseEntity<ApiResponse<List<UserResponse>>> getUsersByIdsCached(
            @Parameter(description = "List of user IDs (max 20 for caching)", required = true)
            @RequestParam("ids") List<Long> userIds) {
        
        try {
            log.info("Cached batch request for {} user IDs", userIds != null ? userIds.size() : 0);
            
            List<UserResponse> users = optimizedUserService.getUsersByIdsCached(userIds);
            
            return ResponseEntity.ok(ApiResponse.success(
                String.format("Retrieved %d users from cache", users.size()), 
                users
            ));
            
        } catch (BatchSizeLimitExceededException e) {
            log.warn("Batch size limit exceeded for cached request: {}", e.getMessage());
            return ResponseEntity.badRequest().body(
                ApiResponse.error("BATCH_SIZE_LIMIT_EXCEEDED", e.getMessage())
            );
        } catch (Exception e) {
            log.error("Error processing cached batch request: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                ApiResponse.error("CACHED_BATCH_ERROR", "Failed to process cached batch request")
            );
        }
    }

    /**
     * Get users by IDs as Map for efficient lookup
     */
    @GetMapping("/batch/map")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Get users as Map", description = "Retrieve users as ID->User map for efficient lookup")
    public ResponseEntity<ApiResponse<Map<Long, UserResponse>>> getUsersByIdsAsMap(
            @Parameter(description = "List of user IDs", required = true)
            @RequestParam("ids") List<Long> userIds) {
        
        try {
            log.info("Map batch request for {} user IDs", userIds != null ? userIds.size() : 0);
            
            Map<Long, UserResponse> userMap = optimizedUserService.getUsersByIdsAsMap(userIds);
            
            return ResponseEntity.ok(ApiResponse.success(
                String.format("Retrieved %d users as map", userMap.size()), 
                userMap
            ));
            
        } catch (BatchSizeLimitExceededException e) {
            log.warn("Batch size limit exceeded for map request: {}", e.getMessage());
            return ResponseEntity.badRequest().body(
                ApiResponse.error("BATCH_SIZE_LIMIT_EXCEEDED", e.getMessage())
            );
        } catch (Exception e) {
            log.error("Error processing map batch request: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                ApiResponse.error("MAP_BATCH_ERROR", "Failed to process map batch request")
            );
        }
    }

    /**
     * Get users by IDs with pagination
     */
    @GetMapping("/batch/paginated")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Get users with pagination", description = "Retrieve users with pagination support")
    public ResponseEntity<ApiResponse<Page<UserResponse>>> getUsersByIdsPaginated(
            @Parameter(description = "List of user IDs", required = true)
            @RequestParam("ids") List<Long> userIds,
            @Parameter(description = "Page number (0-based)")
            @RequestParam(value = "page", defaultValue = "0") @Min(0) int page,
            @Parameter(description = "Page size")
            @RequestParam(value = "size", defaultValue = "20") @Min(1) @Max(100) int size) {
        
        try {
            log.info("Paginated batch request for {} user IDs (page={}, size={})", 
                    userIds != null ? userIds.size() : 0, page, size);
            
            Pageable pageable = PageRequest.of(page, size);
            Page<UserResponse> userPage = optimizedUserService.getUsersByIdsPaginated(userIds, pageable);
            
            return ResponseEntity.ok(ApiResponse.success(
                String.format("Retrieved page %d with %d users", page, userPage.getNumberOfElements()), 
                userPage
            ));
            
        } catch (BatchSizeLimitExceededException e) {
            log.warn("Batch size limit exceeded for paginated request: {}", e.getMessage());
            return ResponseEntity.badRequest().body(
                ApiResponse.error("BATCH_SIZE_LIMIT_EXCEEDED", e.getMessage())
            );
        } catch (Exception e) {
            log.error("Error processing paginated batch request: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                ApiResponse.error("PAGINATED_BATCH_ERROR", "Failed to process paginated batch request")
            );
        }
    }

    /**
     * Check if users exist by IDs
     */
    @GetMapping("/batch/exists")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Check users existence", description = "Check if users exist by their IDs")
    public ResponseEntity<ApiResponse<Map<Long, Boolean>>> checkUsersExist(
            @Parameter(description = "List of user IDs", required = true)
            @RequestParam("ids") List<Long> userIds) {
        
        try {
            log.info("Existence check request for {} user IDs", userIds != null ? userIds.size() : 0);
            
            Map<Long, Boolean> existenceMap = optimizedUserService.checkUsersExist(userIds);
            
            return ResponseEntity.ok(ApiResponse.success(
                String.format("Checked existence for %d users", existenceMap.size()), 
                existenceMap
            ));
            
        } catch (BatchSizeLimitExceededException e) {
            log.warn("Batch size limit exceeded for existence check: {}", e.getMessage());
            return ResponseEntity.badRequest().body(
                ApiResponse.error("BATCH_SIZE_LIMIT_EXCEEDED", e.getMessage())
            );
        } catch (Exception e) {
            log.error("Error processing existence check request: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                ApiResponse.error("EXISTENCE_CHECK_ERROR", "Failed to process existence check request")
            );
        }
    }

    /**
     * Get batch operation statistics
     */
    @GetMapping("/batch/stats")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Get batch statistics", description = "Get statistics for batch operations")
    public ResponseEntity<ApiResponse<OptimizedUserService.BatchStatistics>> getBatchStatistics(
            @Parameter(description = "List of user IDs", required = true)
            @RequestParam("ids") List<Long> userIds) {
        
        try {
            log.info("Statistics request for {} user IDs", userIds != null ? userIds.size() : 0);
            
            OptimizedUserService.BatchStatistics stats = optimizedUserService.getBatchStatistics(userIds);
            
            return ResponseEntity.ok(ApiResponse.success(
                "Batch statistics calculated", 
                stats
            ));
            
        } catch (Exception e) {
            log.error("Error calculating batch statistics: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                ApiResponse.error("STATS_ERROR", "Failed to calculate batch statistics")
            );
        }
    }
}
