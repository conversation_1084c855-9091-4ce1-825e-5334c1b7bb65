package by.algin.api;

import by.algin.dto.response.ApiResponse;
import by.algin.dto.response.PagedResponse;
import by.algin.dto.response.UserResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Common API interface for User operations
 * This interface should be implemented by UserService and used by Feign clients
 */
@RequestMapping("/api/users")
public interface UserApi {

    /**
     * Get user by ID
     * @param userId User ID
     * @return User details
     */
    @GetMapping("/{userId}")
    ApiResponse<UserResponse> getUserById(@PathVariable("userId") Long userId);

    /**
     * Check if user exists
     * @param userId User ID
     * @return Boolean indicating if user exists
     */
    @GetMapping("/{userId}/exists")
    ApiResponse<Boolean> userExists(@PathVariable("userId") Long userId);

    /**
     * Search users by field and value
     * @param field Search field (username, email, etc.)
     * @param value Search value
     * @param page Page number
     * @param size Page size
     * @return Paged list of users
     */
    @GetMapping("/search")
    ApiResponse<PagedResponse<UserResponse>> searchUsers(
            @RequestParam("field") String field,
            @RequestParam("value") String value,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "20") int size
    );

    /**
     * Get users by IDs in batch
     * @param userIds List of user IDs
     * @return List of users
     */
    @GetMapping("/batch")
    ApiResponse<List<UserResponse>> getUsersByIds(@RequestParam("userIds") List<Long> userIds);
}
