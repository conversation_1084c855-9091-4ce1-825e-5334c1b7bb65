package by.algin.util;

import java.util.regex.Pattern;

/**
 * Common validation utilities used across all services
 * Centralizes validation logic to avoid duplication
 */
public final class ValidationUtils {

    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    );

    private static final Pattern USERNAME_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9._-]{3,20}$"
    );

    private static final Pattern PASSWORD_PATTERN = Pattern.compile(
        "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$"
    );

    private ValidationUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * Validate email format
     * @param email Email to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidEmail(String email) {
        return email != null && EMAIL_PATTERN.matcher(email).matches();
    }

    /**
     * Validate username format
     * @param username Username to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidUsername(String username) {
        return username != null && USERNAME_PATTERN.matcher(username).matches();
    }

    /**
     * Validate password strength
     * @param password Password to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidPassword(String password) {
        return password != null && PASSWORD_PATTERN.matcher(password).matches();
    }

    /**
     * Check if string is null or empty
     * @param str String to check
     * @return true if null or empty, false otherwise
     */
    public static boolean isNullOrEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * Check if string is not null and not empty
     * @param str String to check
     * @return true if not null and not empty, false otherwise
     */
    public static boolean isNotNullOrEmpty(String str) {
        return !isNullOrEmpty(str);
    }

    /**
     * Validate project name
     * @param name Project name to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidProjectName(String name) {
        return isNotNullOrEmpty(name) && name.length() >= 2 && name.length() <= 100;
    }

    /**
     * Validate project description
     * @param description Project description to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidProjectDescription(String description) {
        return description == null || description.length() <= 500;
    }

    /**
     * Validate ID (positive number)
     * @param id ID to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidId(Long id) {
        return id != null && id > 0;
    }

    /**
     * Validate pagination parameters
     * @param page Page number
     * @param size Page size
     * @return true if valid, false otherwise
     */
    public static boolean isValidPagination(int page, int size) {
        return page >= 0 && size > 0 && size <= 100;
    }

    /**
     * Sanitize string for safe usage
     * @param input Input string
     * @return Sanitized string
     */
    public static String sanitizeString(String input) {
        if (input == null) {
            return null;
        }
        return input.trim().replaceAll("[<>\"'&]", "");
    }

    /**
     * Validate token format (basic check)
     * @param token Token to validate
     * @return true if valid format, false otherwise
     */
    public static boolean isValidTokenFormat(String token) {
        return isNotNullOrEmpty(token) && token.length() >= 10;
    }
}
