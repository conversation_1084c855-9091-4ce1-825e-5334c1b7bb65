package by.algin.projectservice.constants;

public final class ProjectReasonKeys {

    // Project-related reasons
    public static final String REASON_PROJECT_NOT_FOUND = "project_not_found";
    public static final String REASON_PROJECT_ACCESS_DENIED = "project_access_denied";
    public static final String REASON_DUPLICATE_PROJECT_NAME = "duplicate_project_name";
    public static final String REASON_PROJECT_CREATION_FAILED = "project_creation_failed";
    public static final String REASON_PROJECT_UPDATE_FAILED = "project_update_failed";
    public static final String REASON_PROJECT_DELETION_FAILED = "project_deletion_failed";

    // Project member-related reasons
    public static final String REASON_PROJECT_MEMBER_NOT_FOUND = "project_member_not_found";
    public static final String REASON_DUPLICATE_PROJECT_MEMBER = "duplicate_project_member";
    public static final String REASON_CANNOT_REMOVE_PROJECT_OWNER = "cannot_remove_project_owner";
    public static final String REASON_CANNOT_CHANGE_OWNER_ROLE = "cannot_change_owner_role";
    public static final String REASON_MEMBER_LIMIT_EXCEEDED = "member_limit_exceeded";

    // Project invitation-related reasons
    public static final String REASON_PROJECT_INVITATION_NOT_FOUND = "project_invitation_not_found";
    public static final String REASON_INVITATION_EXPIRED = "invitation_expired";
    public static final String REASON_INVITATION_ALREADY_ACCEPTED = "invitation_already_accepted";
    public static final String REASON_INVITATION_ALREADY_REJECTED = "invitation_already_rejected";

    // Business rule-related reasons
    public static final String REASON_BUSINESS_RULE_VIOLATION = "business_rule_violation";
    public static final String REASON_INSUFFICIENT_PERMISSIONS = "insufficient_permissions";
    public static final String REASON_INVALID_PROJECT_STATUS = "invalid_project_status";

    // Status transition-related reasons
    public static final String REASON_STATUS_TRANSITION_NOT_ALLOWED = "status_transition_not_allowed";
    public static final String REASON_INVALID_STATUS_PARAMETER = "invalid_status_parameter";
    public static final String REASON_STATUS_TRANSITION_CONFIG_ERROR = "status_transition_config_error";

    // Service communication reasons
    public static final String REASON_SERVICE_CALL_FAILED = "service_call_failed";
    public static final String REASON_USER_SERVICE_UNAVAILABLE = "user_service_unavailable";

    // Common reasons (reused from CommonExceptionHandling)
    public static final String REASON_USER_NOT_FOUND = "user_not_found";
    public static final String REASON_RESOURCE_NOT_FOUND = "resource_not_found";
    public static final String REASON_VALIDATION_FAILED = "validation_failed";
    public static final String REASON_VALIDATION_ERROR = "validation_error";
    public static final String REASON_SERVICE_UNAVAILABLE = "service_unavailable";
    public static final String REASON_AUTHENTICATION_FAILED = "authentication_failed";
    public static final String REASON_AUTHORIZATION_FAILED = "authorization_failed";

    // Internal error reasons
    public static final String REASON_INTERNAL_ERROR = "internal_error";
    public static final String REASON_UNEXPECTED_ERROR = "unexpected_error";
    public static final String REASON_CONFIGURATION_ERROR = "configuration_error";

    private ProjectReasonKeys() {}
}
