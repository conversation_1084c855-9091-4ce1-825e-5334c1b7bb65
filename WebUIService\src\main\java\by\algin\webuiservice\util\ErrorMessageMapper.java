package by.algin.webuiservice.util;

import by.algin.webuiservice.constants.ErrorCodeConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;

import java.util.Locale;

@Slf4j
@Component
@RequiredArgsConstructor
public class ErrorMessageMapper {

    private final MessageSource messageSource;

    public String getLocalizedErrorMessage(String errorCode, Locale locale) {
        if (errorCode == null || errorCode.isEmpty()) {
            return getDefaultErrorMessage(locale);
        }

        String messageKey = mapErrorCodeToMessageKey(errorCode);
        
        try {
            return messageSource.getMessage(messageKey, null, locale);
        } catch (Exception e) {
            log.warn("No localized message found for errorCode: {}, using default", errorCode);
            return getDefaultErrorMessage(locale);
        }
    }

    private String mapErrorCodeToMessageKey(String errorCode) {
        return switch (errorCode.toUpperCase()) {

            case ErrorCodeConstants.INVALID_CREDENTIALS -> "error.login.invalid.credentials";
            case ErrorCodeConstants.ACCOUNT_DISABLED -> "error.login.account.disabled";
            case ErrorCodeConstants.ACCOUNT_LOCKED -> "error.login.account.locked";

            case ErrorCodeConstants.EMAIL_ALREADY_EXISTS -> "error.registration.email.exists";
            case ErrorCodeConstants.USERNAME_ALREADY_EXISTS -> "error.registration.username.exists";
            case ErrorCodeConstants.PASSWORDS_DONT_MATCH -> "error.registration.passwords.mismatch";
            case ErrorCodeConstants.INVALID_EMAIL -> "error.registration.email.invalid";

            case ErrorCodeConstants.VALIDATION_ERROR -> "error.validation.failed";

            case ErrorCodeConstants.INVALID_TOKEN -> "error.token.invalid";
            case ErrorCodeConstants.EXPIRED_TOKEN -> "error.token.expired";

            case ErrorCodeConstants.USER_NOT_FOUND -> "error.user.not.found";
            case ErrorCodeConstants.RATE_LIMIT_EXCEEDED -> "error.rate.limit.exceeded";
            case ErrorCodeConstants.INTERNAL_SERVER_ERROR -> "error.server.internal";
            case ErrorCodeConstants.SERVICE_UNAVAILABLE -> "error.service.unavailable";

            case ErrorCodeConstants.REGISTRATION_SUCCESS -> "success.registration";

            default -> "error.general";
        };
    }

    private String getDefaultErrorMessage(Locale locale) {
        return messageSource.getMessage("error.general", null, "An error occurred", locale);
    }
}
