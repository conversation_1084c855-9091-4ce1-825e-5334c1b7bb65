
CREATE TABLE IF NOT EXISTS projects (
    id BIGSERIAL PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(100) NOT NULL,
    description VARCHAR(500),
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    owner_id BIGINT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_project_status CHECK (status IN ('ACTIVE', 'INACTIVE', 'COMPLETED', 'ARCHIVED')),
    CONSTRAINT chk_project_name_length CHECK (LENGTH(name) >= 2)
);

CREATE INDEX IF NOT EXISTS idx_projects_owner_id ON projects(owner_id);
CREATE INDEX IF NOT EXISTS idx_projects_status ON projects(status);
CREATE INDEX IF NOT EXISTS idx_projects_created_at ON projects(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_projects_name ON projects(name);
CREATE UNIQUE INDEX IF NOT EXISTS idx_projects_name_unique ON projects(LOWER(name));

CREATE TABLE IF NOT EXISTS project_roles (
    id BIGSERIAL PRIMARY KEY,
    role_name VARCHAR(50) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    permission_level INTEGER NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    can_be_assigned BOOLEAN NOT NULL DEFAULT true,
    max_per_project INTEGER,
    is_system_role BOOLEAN NOT NULL DEFAULT false,
    color_code VARCHAR(7),
    icon_name VARCHAR(50),
    created_by VARCHAR(100),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100)
);

CREATE INDEX IF NOT EXISTS idx_project_roles_name ON project_roles(role_name);
CREATE INDEX IF NOT EXISTS idx_project_roles_active ON project_roles(is_active);
CREATE INDEX IF NOT EXISTS idx_project_roles_permission_level ON project_roles(permission_level);
CREATE INDEX IF NOT EXISTS idx_project_roles_assignable ON project_roles(can_be_assigned);
CREATE INDEX IF NOT EXISTS idx_project_roles_system ON project_roles(is_system_role);

CREATE TABLE IF NOT EXISTS project_role_permissions (
    id BIGSERIAL PRIMARY KEY,
    role_id BIGINT NOT NULL,
    permission_name VARCHAR(100) NOT NULL,
    is_granted BOOLEAN NOT NULL DEFAULT true,
    description VARCHAR(500),
    created_by VARCHAR(100),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_role_permissions_role FOREIGN KEY (role_id) REFERENCES project_roles(id) ON DELETE CASCADE,
    CONSTRAINT uk_role_permission UNIQUE (role_id, permission_name)
);

CREATE INDEX IF NOT EXISTS idx_role_permissions_role_id ON project_role_permissions(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_name ON project_role_permissions(permission_name);
CREATE INDEX IF NOT EXISTS idx_role_permissions_granted ON project_role_permissions(is_granted);

CREATE TABLE IF NOT EXISTS project_members (
    id BIGSERIAL PRIMARY KEY,
    project_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    joined_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_project_members_project FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    CONSTRAINT fk_project_members_role FOREIGN KEY (role_id) REFERENCES project_roles(id)
);

CREATE UNIQUE INDEX IF NOT EXISTS idx_project_members_unique ON project_members(project_id, user_id);
CREATE INDEX IF NOT EXISTS idx_project_members_project_id ON project_members(project_id);
CREATE INDEX IF NOT EXISTS idx_project_members_user_id ON project_members(user_id);
CREATE INDEX IF NOT EXISTS idx_project_members_role_id ON project_members(role_id);
CREATE INDEX IF NOT EXISTS idx_project_members_joined_at ON project_members(joined_at DESC);
CREATE INDEX IF NOT EXISTS idx_project_members_project_role ON project_members(project_id, role_id);
CREATE INDEX IF NOT EXISTS idx_project_members_user_joined ON project_members(user_id, joined_at DESC);

CREATE TABLE IF NOT EXISTS project_invitations (
    id BIGSERIAL PRIMARY KEY,
    project_id BIGINT NOT NULL,
    invited_email VARCHAR(255),
    accepted_email VARCHAR(255),
    role VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    token VARCHAR(255) NOT NULL UNIQUE,
    invited_by_user_id BIGINT NOT NULL,
    accepted_by_user_id BIGINT,
    message TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    accepted_at TIMESTAMP,

    CONSTRAINT fk_project_invitations_project FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS idx_project_invitations_project_id ON project_invitations(project_id);
CREATE INDEX IF NOT EXISTS idx_project_invitations_email ON project_invitations(invited_email);
CREATE INDEX IF NOT EXISTS idx_project_invitations_status ON project_invitations(status);
CREATE INDEX IF NOT EXISTS idx_project_invitations_token ON project_invitations(token);
CREATE INDEX IF NOT EXISTS idx_project_invitations_expires_at ON project_invitations(expires_at);
CREATE INDEX IF NOT EXISTS idx_project_invitations_invited_by ON project_invitations(invited_by_user_id);
CREATE INDEX IF NOT EXISTS idx_project_invitations_accepted_by ON project_invitations(accepted_by_user_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_project_invitations_unique_pending
    ON project_invitations(project_id, invited_email)
    WHERE status = 'PENDING';

CREATE TABLE IF NOT EXISTS project_status_transitions (
    id BIGSERIAL PRIMARY KEY,
    from_status VARCHAR(50) NOT NULL,
    to_status VARCHAR(50) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    description VARCHAR(500),
    required_role VARCHAR(50),
    valid_from TIMESTAMP,
    valid_to TIMESTAMP,
    created_by VARCHAR(100),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100),
    
    CONSTRAINT uk_status_transition UNIQUE (from_status, to_status)
);

CREATE INDEX IF NOT EXISTS idx_status_transitions_from_status ON project_status_transitions(from_status);
CREATE INDEX IF NOT EXISTS idx_status_transitions_to_status ON project_status_transitions(to_status);
CREATE INDEX IF NOT EXISTS idx_status_transitions_active ON project_status_transitions(is_active);
CREATE INDEX IF NOT EXISTS idx_status_transitions_valid_period ON project_status_transitions(valid_from, valid_to);
CREATE INDEX IF NOT EXISTS idx_status_transitions_created_by ON project_status_transitions(created_by);

COMMENT ON TABLE projects IS 'Core project information and metadata';
COMMENT ON TABLE project_roles IS 'Dynamic project roles that can be configured without code changes';
COMMENT ON TABLE project_role_permissions IS 'Fine-grained permissions for each project role';
COMMENT ON TABLE project_members IS 'Project membership with user-role associations';
COMMENT ON TABLE project_invitations IS 'Project invitation system for adding new members';
COMMENT ON TABLE project_status_transitions IS 'Defines allowed transitions between project statuses';

COMMENT ON COLUMN projects.name IS 'Unique project name (case-insensitive)';
COMMENT ON COLUMN projects.description IS 'Optional project description';
COMMENT ON COLUMN projects.status IS 'Current project status (ACTIVE, INACTIVE, COMPLETED, ARCHIVED)';
COMMENT ON COLUMN projects.owner_id IS 'User ID of the project owner';

COMMENT ON COLUMN project_roles.permission_level IS 'Numeric level for role hierarchy (1=lowest, 10=highest)';
COMMENT ON COLUMN project_roles.max_per_project IS 'Maximum number of users with this role per project (null=unlimited)';
COMMENT ON COLUMN project_roles.is_system_role IS 'System roles cannot be deleted or modified';
COMMENT ON COLUMN project_roles.color_code IS 'Hex color code for UI display';
COMMENT ON COLUMN project_roles.icon_name IS 'Icon identifier for UI display';

COMMENT ON COLUMN project_members.role_id IS 'Reference to dynamic project role from project_roles table';

COMMENT ON COLUMN project_invitations.invited_email IS 'Email address of the intended recipient (optional, for logging purposes)';
COMMENT ON COLUMN project_invitations.accepted_email IS 'Email address of the user who actually accepted the invitation';
COMMENT ON COLUMN project_invitations.role IS 'Role to be assigned when invitation is accepted';
COMMENT ON COLUMN project_invitations.status IS 'Current status of the invitation (PENDING, ACCEPTED, CANCELLED, EXPIRED)';
COMMENT ON COLUMN project_invitations.token IS 'Unique token for invitation acceptance';
COMMENT ON COLUMN project_invitations.invited_by_user_id IS 'ID of the user who sent the invitation';
COMMENT ON COLUMN project_invitations.accepted_by_user_id IS 'ID of the user who accepted the invitation (null if not accepted yet)';
COMMENT ON COLUMN project_invitations.message IS 'Optional message from the inviter to the invitee';
COMMENT ON COLUMN project_invitations.expires_at IS 'Timestamp when invitation expires';
COMMENT ON COLUMN project_invitations.accepted_at IS 'Timestamp when invitation was accepted (null if not accepted)';

COMMENT ON COLUMN project_status_transitions.from_status IS 'Source project status';
COMMENT ON COLUMN project_status_transitions.to_status IS 'Target project status';
COMMENT ON COLUMN project_status_transitions.is_active IS 'Whether this transition is currently enabled';
COMMENT ON COLUMN project_status_transitions.description IS 'Human-readable description of the transition';
COMMENT ON COLUMN project_status_transitions.required_role IS 'Minimum role required to perform this transition';
COMMENT ON COLUMN project_status_transitions.valid_from IS 'When this transition becomes valid (optional)';
COMMENT ON COLUMN project_status_transitions.valid_to IS 'When this transition expires (optional)';
COMMENT ON COLUMN project_status_transitions.created_by IS 'User who created this transition rule';
COMMENT ON COLUMN project_status_transitions.updated_by IS 'User who last updated this transition rule';
