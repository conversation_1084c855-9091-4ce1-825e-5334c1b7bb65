package by.algin.constants;

public final class CommonProjectConstants {

    // Project roles
    public static final String ROLE_OWNER = "OWNER";
    public static final String ROLE_MANAGER = "MANAGER";
    public static final String ROLE_DEVELOPER = "DEVELOPER";
    public static final String ROLE_VIEWER = "VIEWER";

    // Project permissions
    public static final String PROJECT_VIEW = "PROJECT_VIEW";
    public static final String PROJECT_EDIT = "PROJECT_EDIT";
    public static final String PROJECT_DELETE = "PROJECT_DELETE";
    public static final String PROJECT_ARCHIVE = "PROJECT_ARCHIVE";
    public static final String PROJECT_STATUS_CHANGE = "PROJECT_STATUS_CHANGE";

    public static final String MEMBER_VIEW = "MEMBER_VIEW";
    public static final String MEMBER_ADD = "MEMBER_ADD";
    public static final String MEMBER_REMOVE = "MEMBER_REMOVE";
    public static final String MEMBER_ROLE_CHANGE = "MEMBER_ROLE_CHANGE";

    public static final String INVITATION_CREATE = "INVITATION_CREATE";
    public static final String INVITATION_CANCEL = "INVITATION_CANCEL";
    public static final String INVITATION_VIEW = "INVITATION_VIEW";

    public static final String SETTINGS_VIEW = "SETTINGS_VIEW";
    public static final String SETTINGS_EDIT = "SETTINGS_EDIT";

    public static final String ROLE_ASSIGN_OWNER = "ROLE_ASSIGN_OWNER";
    public static final String ROLE_ASSIGN_MANAGER = "ROLE_ASSIGN_MANAGER";
    public static final String ROLE_ASSIGN_DEVELOPER = "ROLE_ASSIGN_DEVELOPER";
    public static final String ROLE_ASSIGN_VIEWER = "ROLE_ASSIGN_VIEWER";

    public static final String AUDIT_VIEW = "AUDIT_VIEW";
    public static final String METRICS_VIEW = "METRICS_VIEW";

    public static final String[] ALL_PERMISSIONS = {
        PROJECT_VIEW, PROJECT_EDIT, PROJECT_DELETE, PROJECT_ARCHIVE, PROJECT_STATUS_CHANGE,
        MEMBER_VIEW, MEMBER_ADD, MEMBER_REMOVE, MEMBER_ROLE_CHANGE,
        INVITATION_CREATE, INVITATION_CANCEL, INVITATION_VIEW,
        SETTINGS_VIEW, SETTINGS_EDIT,
        ROLE_ASSIGN_OWNER, ROLE_ASSIGN_MANAGER, ROLE_ASSIGN_DEVELOPER, ROLE_ASSIGN_VIEWER,
        AUDIT_VIEW, METRICS_VIEW
    };

    // Project status transitions
    public static final String STATUS_ACTIVE = "ACTIVE";
    public static final String STATUS_ARCHIVED = "ARCHIVED";
    public static final String STATUS_COMPLETED = "COMPLETED";
    public static final String STATUS_INACTIVE = "INACTIVE";

    // Validation helper method
    public static boolean isValidPermission(String permission) {
        if (permission == null || permission.trim().isEmpty()) {
            return false;
        }

        for (String validPermission : ALL_PERMISSIONS) {
            if (validPermission.equals(permission)) {
                return true;
            }
        }
        return false;
    }

    private CommonProjectConstants() {
    }
}
