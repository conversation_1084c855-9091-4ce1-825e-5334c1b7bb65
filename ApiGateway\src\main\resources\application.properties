spring.application.name=API-GATEWAY
server.port=8080
eureka.client.serviceUrl.defaultZone=http://localhost:8761/eureka/

logging.level.org.springframework.cloud.gateway=DEBUG
logging.level.org.springframework.web.servlet.DispatcherServlet=DEBUG

spring.cloud.gateway.routes[0].id=web_ui_auth_route
spring.cloud.gateway.routes[0].uri=lb://WEB-UI-SERVICE
spring.cloud.gateway.routes[0].predicates[0]=Path=/auth/**
spring.cloud.gateway.routes[0].filters[0]=RewriteLocationResponseHeader=

spring.cloud.gateway.routes[1].id=web_ui_pages_route
spring.cloud.gateway.routes[1].uri=lb://WEB-UI-SERVICE
spring.cloud.gateway.routes[1].predicates[0]=Path=/dashboard/**
spring.cloud.gateway.routes[1].predicates[1]=Path=/admin-dashboard/**
spring.cloud.gateway.routes[1].predicates[2]=Path=/projects/**
spring.cloud.gateway.routes[1].predicates[3]=Path=/

spring.cloud.gateway.routes[2].id=user_static_resources_route
spring.cloud.gateway.routes[2].uri=lb://USER-SERVICE
spring.cloud.gateway.routes[2].predicates[0]=Path=/css/**
spring.cloud.gateway.routes[2].predicates[1]=Path=/js/**
spring.cloud.gateway.routes[2].predicates[2]=Path=/images/**

spring.cloud.gateway.routes[3].id=user_api_route
spring.cloud.gateway.routes[3].uri=lb://USER-SERVICE
spring.cloud.gateway.routes[3].predicates[0]=Path=/api/users/**

spring.cloud.gateway.routes[4].id=project_api_route
spring.cloud.gateway.routes[4].uri=lb://PROJECT-SERVICE
spring.cloud.gateway.routes[4].predicates[0]=Path=/api/projects/**

