<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title th:text="#{admin.dashboard.title}">Admin Dashboard</title>
    <link rel="stylesheet" th:href="@{/css/variables.css}">
    <link rel="stylesheet" th:href="@{/css/base.css}">
    <link rel="stylesheet" th:href="@{/css/admin.css}">
</head>
<body>
    <div class="language-switcher">
        <a th:href="@{/setLanguage(lang='en')}" th:text="#{language.english}">English</a> |
        <a th:href="@{/setLanguage(lang='ru')}" th:text="#{language.russian}">Russian</a>
    </div>

    <div th:if="${error}">
        <p class="error-message" th:text="${error}"></p>
    </div>
    <div th:if="${success}">
        <p class="success-message" th:text="${success}"></p>
    </div>

    <h1 th:text="#{admin.dashboard.welcome}">Admin Dashboard</h1>

    <form th:action="@{/auth/logout}" method="post">
        <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}" />
        <button type="submit" class="logout-button" th:text="#{nav.logout}">Logout</button>
    </form>
</body>
</html>