package by.algin.userservice.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration properties for batch processing
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app.batch")
public class BatchConfig {
    
    /**
     * Maximum allowed batch size
     */
    private int maxSize = 100;
    
    /**
     * Default chunk size for processing large batches
     */
    private int defaultChunkSize = 50;
    
    /**
     * Whether to cache small batches
     */
    private boolean cacheSmallBatches = true;
    
    /**
     * Threshold for considering a batch as "small" for caching
     */
    private int smallBatchThreshold = 20;
    
    /**
     * Enable batch processing metrics
     */
    private boolean enableMetrics = true;
    
    /**
     * Timeout for batch operations in seconds
     */
    private int timeoutSeconds = 30;
}
