package by.algin.common;

import by.algin.dto.response.ApiResponse;
import by.algin.common.util.ErrorMappingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;
import java.util.Map;

/**
 * Minimal base exception handler for all microservices
 *
 * Provides only essential fallback handlers and common helper methods.
 * Each microservice should extend this class and add all specific handlers they need.
 *
 * This approach ensures:
 * - No conflicts between handlers
 * - Each service controls its own error handling
 * - Common helper methods are reused
 * - Fallback for unexpected exceptions
 */
@Slf4j
@RestControllerAdvice
public abstract class BaseExceptionHandler {

    @Autowired(required = false)
    protected ErrorMappingService errorMappingService;

    /**
     * Each microservice must provide its service name for error tracking
     */
    protected abstract String getServiceName();

    // ========== FALLBACK HANDLERS ==========
    // These handle exceptions that don't have specific handlers in microservices

    /**
     * Handles Spring validation errors (e.g., @Valid annotations)
     * Microservices can override this if they need custom validation handling
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Object>> handleValidation(MethodArgumentNotValidException ex) {
        log.warn("Validation failed in {}: {}", getServiceName(), ex.getMessage());

        Map<String, String> fieldErrors = new HashMap<>();
        for (FieldError error : ex.getBindingResult().getFieldErrors()) {
            fieldErrors.put(error.getField(), error.getDefaultMessage());
        }

        Map<String, Object> errorDetails = createReasonDetails("validation_failed", ex);
        errorDetails.put("fieldErrors", fieldErrors);

        return createError("VALIDATION_ERROR", "Validation failed", 400, errorDetails);
    }

    // Note: RuntimeException handler removed to avoid conflicts with specific handlers in microservices
    // Each microservice should handle its specific RuntimeExceptions
    // Only the most generic Exception handler remains as ultimate fallback

    /**
     * Handles all other unhandled exceptions
     * This is the ultimate fallback for any exception not caught elsewhere
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Object>> handleGeneral(Exception ex) {
        log.error("Unhandled exception in {}: {}", getServiceName(), ex.getMessage(), ex);

        Map<String, Object> errorDetails = createReasonDetails("unexpected_error", ex);
        return createError("UNEXPECTED_ERROR", "Unexpected error occurred", 500, errorDetails);
    }

    // ========== HELPER METHODS ==========
    // These methods are available to all microservices for consistent error handling

    /**
     * Creates a standardized error response without additional details
     *
     * @param code Error code (e.g., "USER_NOT_FOUND")
     * @param message Human-readable error message
     * @param status HTTP status code
     * @return ResponseEntity with standardized ApiResponse
     */
    protected ResponseEntity<ApiResponse<Object>> createError(String code, String message, int status) {
        ApiResponse<Object> response = ApiResponse.error(code, message);
        response.setServiceName(getServiceName());
        return ResponseEntity.status(status).body(response);
    }



    /**
     * Creates a standardized error response with additional details
     *
     * @param code Error code (e.g., "VALIDATION_ERROR")
     * @param message Human-readable error message
     * @param status HTTP status code
     * @param details Additional error details (e.g., field errors, debug info)
     * @return ResponseEntity with standardized ApiResponse
     */
    protected ResponseEntity<ApiResponse<Object>> createError(String code, String message, int status, Map<String, Object> details) {
        ApiResponse<Object> response = ApiResponse.error(code, message);
        response.setServiceName(getServiceName());
        response.setErrorDetails(details);
        return ResponseEntity.status(status).body(response);
    }

    /**
     * Creates standard reason details for error responses
     * This provides consistent error metadata across all microservices
     *
     * @param reasonKey Reason key for categorizing the error (e.g., "user_not_found")
     * @param ex The exception that occurred
     * @return Map with standard error details
     */
    protected Map<String, Object> createReasonDetails(String reasonKey, Exception ex) {
        Map<String, Object> details = new HashMap<>();
        details.put("reason", reasonKey);
        details.put("exceptionType", ex.getClass().getSimpleName());
        details.put("serviceName", getServiceName());
        details.put("timestamp", System.currentTimeMillis());
        return details;
    }

    /**
     * Maps service-specific error to common error format
     */
    protected ResponseEntity<ApiResponse<Object>> createMappedError(Exception ex, String fallbackCode, String fallbackMessage, int fallbackStatus) {
        if (errorMappingService != null) {
            var mappedError = errorMappingService.mapError(ex, getServiceName());
            if (mappedError != null) {
                Map<String, Object> details = createReasonDetails(mappedError.getCode().toLowerCase(), ex);
                return createError(mappedError.getCode(), mappedError.getMessage(), mappedError.getStatus(), details);
            }
        }
        Map<String, Object> details = createReasonDetails(fallbackCode.toLowerCase(), ex);
        return createError(fallbackCode, fallbackMessage, fallbackStatus, details);
    }
}
