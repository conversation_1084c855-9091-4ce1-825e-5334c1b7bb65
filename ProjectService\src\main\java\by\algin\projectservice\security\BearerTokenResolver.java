package by.algin.projectservice.security;

import by.algin.projectservice.exception.JwtAuthenticationException;
import by.algin.projectservice.exception.ProjectErrorCodes;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
@Slf4j
public class BearerTokenResolver {
    
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String BEARER_PREFIX = "Bearer ";

    public String resolve(HttpServletRequest request) {
        String authorizationHeader = request.getHeader(AUTHORIZATION_HEADER);

        if (!StringUtils.hasText(authorizationHeader)) {
            log.debug("Authorization header is missing");
            throw new JwtAuthenticationException(
                ProjectErrorCodes.JWT_TOKEN_MISSING,
                "Authorization header is missing",
                new RuntimeException("Missing authorization header")
            );
        }

        if (!authorizationHeader.startsWith(BEARER_PREFIX)) {
            log.debug("Authorization header does not start with 'Bearer '");
            throw new JwtAuthenticationException(
                ProjectErrorCodes.JWT_TOKEN_MISSING,
                "Authorization header must start with 'Bearer '",
                new RuntimeException("Invalid authorization header format")
            );
        }

        String token = authorizationHeader.substring(BEARER_PREFIX.length());

        if (!StringUtils.hasText(token)) {
            log.debug("Bearer token is empty");
            throw new JwtAuthenticationException(
                ProjectErrorCodes.JWT_TOKEN_MISSING,
                "Bearer token is empty",
                new RuntimeException("Empty bearer token")
            );
        }

        log.debug("Successfully resolved Bearer token");
        return token;
    }

    public boolean hasToken(HttpServletRequest request) {
        try {
            resolve(request);
            return true;
        } catch (JwtAuthenticationException e) {
            return false;
        }
    }
}
