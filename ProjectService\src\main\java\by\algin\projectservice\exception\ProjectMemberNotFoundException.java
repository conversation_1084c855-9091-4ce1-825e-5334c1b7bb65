package by.algin.projectservice.exception;

import lombok.Getter;

@Getter
public class ProjectMemberNotFoundException extends RuntimeException {
    private final Long projectId;
    private final Long userId;

    public ProjectMemberNotFoundException(Long projectId, Long userId) {
        super("Project member not found for project ID: " + projectId + " and user ID: " + userId);
        this.projectId = projectId;
        this.userId = userId;
    }
}
