package by.algin.projectservice.security;

import by.algin.projectservice.constants.ProjectMessageConstants;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.UnsupportedJwtException;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.function.Function;

@Component
@Slf4j
public class JwtUtil {

    private final SecretKey secretKey;
    private final long jwtExpiration;

    public JwtUtil(@Value("${app.jwt.secret}") String secret,
                   @Value("${app.jwt.expiration}") long jwtExpiration) {
        this.secretKey = Keys.hmacShaKeyFor(secret.getBytes(StandardCharsets.UTF_8));
        this.jwtExpiration = jwtExpiration;
    }

    public Long extractUserId(String token) {
        try {
            String userIdStr = extractClaim(token, claims -> claims.get("userId", String.class));
            if (userIdStr == null) {
                userIdStr = extractClaim(token, Claims::getSubject);
            }
            return userIdStr != null ? Long.parseLong(userIdStr) : null;
        } catch (NumberFormatException e) {
            log.error(ProjectMessageConstants.INVALID_USER_ID_FORMAT_IN_JWT, e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("Failed to extract user ID from JWT token: {}", e.getMessage(), e);
            return null;
        }
    }

    public String extractUsername(String token) {
        log.debug("Starting extractUsername method");
        try {
            String username = extractClaim(token, claims -> claims.get("username", String.class));
            if (username == null) {

                username = extractClaim(token, Claims::getSubject);
            }
            log.debug("extractUsername returning: {}", username);
            return username;
        } catch (Exception e) {
            log.error("Failed to extract username from JWT token: {}", e.getMessage());
            log.debug("Exception type: {}", e.getClass().getSimpleName());
            return null;
        }
    }

    public String extractEmail(String token) {
        return extractClaim(token, claims -> claims.get("email", String.class));
    }

    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        log.debug("Starting extractClaim method");
        try {
            log.debug("Extracting claim from token: {}", token);
            final Claims claims = extractAllClaims(token);
            log.debug("Successfully extracted claims");
            T result = claimsResolver.apply(claims);
            log.debug("Returning result from extractClaim: {}", result);
            return result;
        } catch (Exception e) {
            log.error("Failed to extract claim from JWT token: {}", e.getMessage());
            log.debug("Exception type: {}", e.getClass().getSimpleName());
            log.debug("Returning null from extractClaim");
            return null;
        }
    }

    private Claims extractAllClaims(String token) {
        return Jwts.parser()
                .verifyWith(secretKey)
                .build()
                .parseSignedClaims(token)
                .getPayload();
    }

    public Boolean isTokenExpired(String token) {
        try {
            return extractExpiration(token).before(new Date());
        } catch (Exception e) {
            log.error("Error checking token expiration: {}", e.getMessage());
            return true;
        }
    }

    public Boolean validateToken(String token) {
        try {
            if (token == null || token.trim().isEmpty()) {
                return false;
            }

            extractAllClaims(token);
            return !isTokenExpired(token);
        } catch (MalformedJwtException e) {
            log.error(ProjectMessageConstants.INVALID_JWT_TOKEN, e.getMessage());
        } catch (UnsupportedJwtException e) {
            log.error("JWT token is unsupported: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
            log.error("JWT claims string is empty: {}", e.getMessage());
        } catch (Exception e) {
            log.error("JWT token validation failed: {}", e.getMessage());
        }
        return false;
    }

    public String generateToken(Long userId, String username) {
        return Jwts.builder()
                .subject(username)
                .claim("userId", userId.toString())
                .claim("username", username)
                .issuedAt(new Date(System.currentTimeMillis()))
                .expiration(new Date(System.currentTimeMillis() + jwtExpiration))
                .signWith(secretKey)
                .compact();
    }
}
