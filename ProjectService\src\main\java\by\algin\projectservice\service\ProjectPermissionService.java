package by.algin.projectservice.service;

import by.algin.constants.CommonProjectConstants;
import by.algin.projectservice.entity.ProjectMember;
import by.algin.projectservice.entity.ProjectRole;
import by.algin.projectservice.exception.ProjectAccessDeniedException;
import by.algin.projectservice.repository.ProjectMemberRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;


@Service
@RequiredArgsConstructor
@Slf4j
public class ProjectPermissionService {

    private final ProjectMemberRepository projectMemberRepository;
    private final ProjectRoleService roleService;

    public boolean hasPermission(Long projectId, Long userId, String permission) {
        log.debug("Checking permission '{}' for user {} on project {}", permission, userId, projectId);

        Optional<ProjectMember> member = projectMemberRepository.findByProjectIdAndUserId(projectId, userId);
        if (member.isEmpty()) {
            log.debug("User {} is not a member of project {}", userId, projectId);
            return false;
        }

        String roleName = member.get().getRole().getRoleName();
        boolean hasPermission = roleService.hasPermission(roleName, permission);

        log.debug("User {} with role '{}' has permission '{}' on project {}: {}",
                userId, roleName, permission, projectId, hasPermission);

        return hasPermission;
    }

    public boolean canViewProject(Long projectId, Long userId) {
        return hasPermission(projectId, userId, CommonProjectConstants.PROJECT_VIEW);
    }

    public boolean canModifyProject(Long projectId, Long userId) {
        return hasPermission(projectId, userId, CommonProjectConstants.PROJECT_EDIT);
    }

    public boolean canManageMembers(Long projectId, Long userId) {
        return hasPermission(projectId, userId, CommonProjectConstants.MEMBER_ADD) ||
               hasPermission(projectId, userId, CommonProjectConstants.MEMBER_REMOVE);
    }


    public boolean canDeleteProject(Long projectId, Long userId) {
        return hasPermission(projectId, userId, CommonProjectConstants.PROJECT_DELETE);
    }

    public void requirePermission(Long projectId, Long userId, String permission) {
        if (!hasPermission(projectId, userId, permission)) {
            log.warn("Access denied: User {} lacks permission '{}' for project {}", userId, permission, projectId);
            throw new ProjectAccessDeniedException(userId, projectId);
        }
    }

    public void requireViewAccess(Long projectId, Long userId) {
        requirePermission(projectId, userId, CommonProjectConstants.PROJECT_VIEW);
    }

    public void requireModifyAccess(Long projectId, Long userId) {
        requirePermission(projectId, userId, CommonProjectConstants.PROJECT_EDIT);
    }

    public void requireMemberManagementAccess(Long projectId, Long userId) {
        if (!canManageMembers(projectId, userId)) {
            log.warn("Access denied: User {} cannot manage members of project {}", userId, projectId);
            throw new ProjectAccessDeniedException(userId, projectId);
        }
    }


    public void requireDeleteAccess(Long projectId, Long userId) {
        requirePermission(projectId, userId, CommonProjectConstants.PROJECT_DELETE);
    }

    public ProjectMember getProjectMemberOrThrow(Long projectId, Long userId) {
        return projectMemberRepository.findByProjectIdAndUserId(projectId, userId)
                .orElseThrow(() -> {
                    log.warn("User {} is not a member of project {}", userId, projectId);
                    return new ProjectAccessDeniedException(userId, projectId);
                });
    }

    public Optional<ProjectRole> getUserRoleInProject(Long projectId, Long userId) {
        return projectMemberRepository.findByProjectIdAndUserId(projectId, userId)
                .map(ProjectMember::getRole);
    }

    public boolean isMember(Long projectId, Long userId) {
        return projectMemberRepository.existsByProjectIdAndUserId(projectId, userId);
    }

    public Optional<java.util.Set<String>> getUserPermissions(Long projectId, Long userId) {
        return getUserRoleInProject(projectId, userId)
                .map(role -> roleService.getRolePermissionNames(role.getRoleName()));
    }
}
