package by.algin.projectservice.exception;

import by.algin.common.BaseExceptionHandler;
import by.algin.common.FeignErrorDecoder;
import by.algin.common.dto.ErrorDetailsDTO;
import by.algin.common.dto.ErrorDetailsDTOBuilder;
import by.algin.common.exception.ApiErrorCode;
import by.algin.common.exception.UserNotFoundException;
import by.algin.common.exception.ValidationException;
import by.algin.common.exception.ServiceUnavailableException;
import by.algin.dto.response.ApiResponse;
import by.algin.projectservice.constants.ProjectMessageConstants;
import by.algin.projectservice.constants.ProjectReasonKeys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestControllerAdvice
public class ProjectExceptionHandler extends BaseExceptionHandler {

    private static final int BAD_GATEWAY_STATUS = 502;
    private static final int TOKEN_MASK_LENGTH = 10;
    private static final String TOKEN_MASK_SUFFIX = "...";
    private static final String TOKEN_MASK_FALLBACK = "***";



    @Override
    protected String getServiceName() {
        return ProjectMessageConstants.SERVICE_NAME;
    }

    private ResponseEntity<ApiResponse<Object>> handleBusinessException(ApiErrorCode errorCode, String reasonKey, Exception ex) {
        log.warn("{}: {}", errorCode.getDefaultMessage(), ex.getMessage());
        return createDetailedErrorResponse(errorCode, reasonKey, ex, true);
    }

    private ResponseEntity<ApiResponse<Object>> createDetailedErrorResponse(ApiErrorCode errorCode, String reasonKey, Exception ex, boolean includeOriginalMessage) {
        ErrorDetailsDTO errorDetails = ErrorDetailsDTOBuilder.fromApiErrorCode(errorCode)
                .serviceName(getServiceName())
                .reasonKey(reasonKey)
                .exceptionType(ex)
                .generateTraceId()
                .originalMessage(includeOriginalMessage ? ex : null)
                .build();

        return createErrorResponseFromDTO(errorDetails);
    }

    private ResponseEntity<ApiResponse<Object>> createErrorResponseFromDTO(ErrorDetailsDTO errorDetails) {
        Map<String, Object> details = new HashMap<>();
        details.put(ProjectMessageConstants.DETAIL_KEY_REASON, errorDetails.getReasonKey());
        details.put(ProjectMessageConstants.DETAIL_KEY_EXCEPTION_TYPE, errorDetails.getExceptionType());
        details.put(ProjectMessageConstants.DETAIL_KEY_TRACE_ID, errorDetails.getTraceId());
        details.put(ProjectMessageConstants.DETAIL_KEY_SERVICE_NAME, errorDetails.getServiceName());
        details.put(ProjectMessageConstants.DETAIL_KEY_CATEGORY, errorDetails.getCategory());

        if (errorDetails.getOriginalMessage() != null) {
            details.put(ProjectMessageConstants.DETAIL_KEY_ORIGINAL_MESSAGE, errorDetails.getOriginalMessage());
        }

        if (errorDetails.getAdditionalDetails() != null) {
            details.putAll(errorDetails.getAdditionalDetails());
        }

        return createError(errorDetails.getErrorCode(), errorDetails.getMessage(), errorDetails.getStatus(), details);
    }

    private ResponseEntity<ApiResponse<Object>> createDetailedErrorResponseWithDetails(
            ApiErrorCode errorCode, String reasonKey, Exception ex, Map<String, Object> additionalDetails) {

        ErrorDetailsDTO errorDetails = ErrorDetailsDTOBuilder.fromApiErrorCode(errorCode)
                .serviceName(getServiceName())
                .reasonKey(reasonKey)
                .exceptionType(ex)
                .generateTraceId()
                .originalMessage(ex)
                .additionalDetails(additionalDetails != null ? additionalDetails : Map.of())
                .build();

        return createErrorResponseFromDTO(errorDetails);
    }

    @ExceptionHandler(ProjectNotFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleProjectNotFound(ProjectNotFoundException ex) {
        return createDetailedErrorResponseWithDetails(
            ProjectErrorCodes.PROJECT_NOT_FOUND,
            ProjectReasonKeys.REASON_PROJECT_NOT_FOUND,
            ex,
            Map.of(ProjectMessageConstants.DETAIL_KEY_PROJECT_ID, ex.getProjectId())
        );
    }

    @ExceptionHandler(ProjectAccessDeniedException.class)
    public ResponseEntity<ApiResponse<Object>> handleProjectAccessDenied(ProjectAccessDeniedException ex) {
        return createDetailedErrorResponseWithDetails(
            ProjectErrorCodes.PROJECT_ACCESS_DENIED,
            ProjectReasonKeys.REASON_PROJECT_ACCESS_DENIED,
            ex,
            Map.of(
                ProjectMessageConstants.DETAIL_KEY_PROJECT_ID, ex.getProjectId(),
                ProjectMessageConstants.DETAIL_KEY_USER_ID, ex.getUserId()
            )
        );
    }

    @ExceptionHandler(ProjectMemberNotFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleProjectMemberNotFound(ProjectMemberNotFoundException ex) {
        return createDetailedErrorResponseWithDetails(
            ProjectErrorCodes.PROJECT_MEMBER_NOT_FOUND,
            ProjectReasonKeys.REASON_PROJECT_MEMBER_NOT_FOUND,
            ex,
            Map.of(
                ProjectMessageConstants.DETAIL_KEY_PROJECT_ID, ex.getProjectId(),
                ProjectMessageConstants.DETAIL_KEY_USER_ID, ex.getUserId()
            )
        );
    }

    @ExceptionHandler(DuplicateProjectMemberException.class)
    public ResponseEntity<ApiResponse<Object>> handleDuplicateProjectMember(DuplicateProjectMemberException ex) {
        return createDetailedErrorResponseWithDetails(
            ProjectErrorCodes.DUPLICATE_PROJECT_MEMBER,
            ProjectReasonKeys.REASON_DUPLICATE_PROJECT_MEMBER,
            ex,
            Map.of(
                ProjectMessageConstants.DETAIL_KEY_PROJECT_ID, ex.getProjectId(),
                ProjectMessageConstants.DETAIL_KEY_USER_ID, ex.getUserId()
            )
        );
    }

    @ExceptionHandler(DuplicateProjectNameException.class)
    public ResponseEntity<ApiResponse<Object>> handleDuplicateProjectName(DuplicateProjectNameException ex) {
        return createDetailedErrorResponseWithDetails(
            ProjectErrorCodes.DUPLICATE_PROJECT_NAME,
            ProjectReasonKeys.REASON_DUPLICATE_PROJECT_NAME,
            ex,
            Map.of(ProjectMessageConstants.DETAIL_KEY_PROJECT_NAME, ex.getProjectName())
        );
    }

    @ExceptionHandler(ProjectInvitationNotFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleProjectInvitationNotFound(ProjectInvitationNotFoundException ex) {
        return createDetailedErrorResponseWithDetails(
            ProjectErrorCodes.PROJECT_INVITATION_NOT_FOUND,
            ProjectReasonKeys.REASON_PROJECT_INVITATION_NOT_FOUND,
            ex,
            Map.of(
                ProjectMessageConstants.DETAIL_KEY_INVITATION_ID, ex.getInvitationId(),
                ProjectMessageConstants.DETAIL_KEY_TOKEN, ex.getToken()
            )
        );
    }

    @ExceptionHandler(BusinessRuleViolationException.class)
    public ResponseEntity<ApiResponse<Object>> handleBusinessRuleViolation(BusinessRuleViolationException ex) {
        return handleBusinessException(ProjectErrorCodes.BUSINESS_RULE_VIOLATION,
                                     ProjectReasonKeys.REASON_BUSINESS_RULE_VIOLATION, ex);
    }

    @ExceptionHandler(InvalidStatusTransitionException.class)
    public ResponseEntity<ApiResponse<Object>> handleInvalidStatusTransition(InvalidStatusTransitionException ex) {
        Map<String, Object> details = new HashMap<>();
        details.put(ProjectMessageConstants.DETAIL_KEY_FROM_STATUS, ex.getFromStatus());
        details.put(ProjectMessageConstants.DETAIL_KEY_TO_STATUS, ex.getToStatus());
        if (ex.getUserRole() != null) {
            details.put(ProjectMessageConstants.DETAIL_KEY_USER_ROLE, ex.getUserRole());
        }

        return createDetailedErrorResponseWithDetails(
            ProjectErrorCodes.STATUS_TRANSITION_NOT_ALLOWED,
            ProjectReasonKeys.REASON_STATUS_TRANSITION_NOT_ALLOWED,
            ex,
            details
        );
    }

    @ExceptionHandler(FeignErrorDecoder.ServiceCallException.class)
    public ResponseEntity<ApiResponse<Object>> handleServiceCallException(FeignErrorDecoder.ServiceCallException ex) {
        log.warn("Service call failed: {} - {} (HTTP {})", ex.getServiceName(), ex.getMessage(), ex.getHttpStatus());

        ErrorDetailsDTO errorDetails = ErrorDetailsDTOBuilder.create()
                .errorCode("SERVICE_CALL_FAILED")
                .message("Failed to communicate with " + ex.getServiceName() + ": " + ex.getMessage())
                .status(BAD_GATEWAY_STATUS)
                .category("SERVICE")
                .serviceName(getServiceName())
                .reasonKey(ProjectReasonKeys.REASON_SERVICE_CALL_FAILED)
                .exceptionType(ex)
                .originalMessage(ex)
                .generateTraceId()
                .additionalDetails(Map.of(
                    ProjectMessageConstants.DETAIL_KEY_TARGET_SERVICE, ex.getServiceName(),
                    ProjectMessageConstants.DETAIL_KEY_ORIGINAL_ERROR_CODE, ex.getErrorCode(),
                    ProjectMessageConstants.DETAIL_KEY_HTTP_STATUS, ex.getHttpStatus()
                ))
                .build();

        return createErrorResponseFromDTO(errorDetails);
    }

    @ExceptionHandler(UserNotFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleUserNotFound(UserNotFoundException ex) {
        log.warn("User not found: {}", ex.getMessage());
        return createMappedError(ex, "USER_NOT_FOUND", ex.getMessage(), 404);
    }

    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<ApiResponse<Object>> handleValidationException(ValidationException ex) {
        log.warn("Validation exception: {}", ex.getMessage());

        Map<String, Object> details = new HashMap<>();
        if (ex.getErrors() != null && !ex.getErrors().isEmpty()) {
            details.put(ProjectMessageConstants.DETAIL_KEY_FIELD_ERRORS, ex.getErrors());
        }

        return createSimpleErrorResponseWithDetails("VALIDATION_FAILED", ex.getMessage(), 400, "VALIDATION",
                                                   ProjectReasonKeys.REASON_VALIDATION_FAILED, ex, details);
    }

    @ExceptionHandler(ServiceUnavailableException.class)
    public ResponseEntity<ApiResponse<Object>> handleServiceUnavailable(ServiceUnavailableException ex) {
        log.error("Service unavailable: {}", ex.getMessage());
        return createSimpleErrorResponse("SERVICE_UNAVAILABLE", ex.getMessage(), 503, "SERVICE",
                                        ProjectReasonKeys.REASON_SERVICE_UNAVAILABLE, ex);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiResponse<Object>> handleIllegalArgument(IllegalArgumentException ex) {
        log.warn("Validation error: {}", ex.getMessage());
        return createMappedError(ex, "VALIDATION_ERROR", ex.getMessage(), 400);
    }

    @ExceptionHandler(RoleConfigurationException.class)
    public ResponseEntity<ApiResponse<Object>> handleRoleConfiguration(RoleConfigurationException ex) {
        log.warn("Role configuration error: {}", ex.getMessage());

        Map<String, Object> details = new HashMap<>();
        details.put("configurationError", true);
        if (ex.getConfigurationSection() != null) {
            details.put("configurationSection", ex.getConfigurationSection());
        }
        if (ex.getRoleName() != null) {
            details.put("roleName", ex.getRoleName());
        }
        if (ex.getErrors() != null) {
            details.put("validationErrors", ex.getErrors());
        }

        return createSimpleErrorResponseWithDetails("ROLE_CONFIGURATION_ERROR", ex.getMessage(), 400, "CONFIGURATION",
                                                   ProjectReasonKeys.REASON_VALIDATION_ERROR, ex, details);
    }

    @ExceptionHandler(RoleNotFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleRoleNotFound(RoleNotFoundException ex) {
        log.warn("Role not found: {}", ex.getMessage());

        Map<String, Object> details = new HashMap<>();
        details.put("roleNotFound", true);
        details.put("roleName", ex.getRoleName());

        return createSimpleErrorResponseWithDetails("ROLE_NOT_FOUND", ex.getMessage(), 404, "ROLE",
                                                   ProjectReasonKeys.REASON_RESOURCE_NOT_FOUND, ex, details);
    }

    @ExceptionHandler(JwtAuthenticationException.class)
    public ResponseEntity<ApiResponse<Object>> handleJwtAuthentication(JwtAuthenticationException ex) {
        Map<String, Object> details = new HashMap<>();
        if (ex.getToken() != null && !ex.getToken().isEmpty()) {
            String maskedToken = ex.getToken().length() > TOKEN_MASK_LENGTH
                ? ex.getToken().substring(0, TOKEN_MASK_LENGTH) + TOKEN_MASK_SUFFIX
                : TOKEN_MASK_FALLBACK;
            details.put("tokenPrefix", maskedToken);
        }
        details.put("authenticationError", true);

        return createDetailedErrorResponseWithDetails(
            ex.getErrorCode(),
            ProjectReasonKeys.REASON_AUTHENTICATION_FAILED,
            ex,
            details
        );
    }

    private ResponseEntity<ApiResponse<Object>> createSimpleErrorResponse(
            String errorCode, String message, int status, String category, String reasonKey, Exception ex) {
        return createSimpleErrorResponseWithDetails(errorCode, message, status, category, reasonKey, ex, Map.of());
    }

    private ResponseEntity<ApiResponse<Object>> createSimpleErrorResponseWithDetails(
            String errorCode, String message, int status, String category, String reasonKey,
            Exception ex, Map<String, Object> additionalDetails) {

        ErrorDetailsDTO errorDetails = ErrorDetailsDTOBuilder.create()
                .errorCode(errorCode)
                .message(message)
                .status(status)
                .category(category)
                .serviceName(getServiceName())
                .reasonKey(reasonKey)
                .exceptionType(ex)
                .originalMessage(ex)
                .generateTraceId()
                .additionalDetails(additionalDetails != null ? additionalDetails : Map.of())
                .build();

        return createErrorResponseFromDTO(errorDetails);
    }

}
