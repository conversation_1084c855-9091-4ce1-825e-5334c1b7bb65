.project-header {
    padding-bottom: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    border-bottom: 2px solid var(--color-primary);
}

.project-header.edit {
    border-bottom-color: var(--color-warning);
}

.project-header.members {
    border-bottom-color: var(--color-info);
}

.project-title {
    color: var(--color-primary);
    margin: 0 0 var(--spacing-sm) 0;
}

.project-title.edit {
    color: var(--color-warning);
}

.project-title.members {
    color: var(--color-info);
}

.section {
    margin-bottom: var(--spacing-xxl);
}

.section-title {
    color: var(--text-secondary);
    border-bottom: 1px solid var(--border-color-light);
    padding-bottom: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.project-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.meta-item {
    background-color: var(--bg-light);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    border-left: 4px solid var(--color-primary);
}

.meta-label {
    font-weight: bold;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.meta-value {
    color: var(--text-muted);
}

.status-active { color: var(--status-active); }
.status-inactive { color: var(--status-inactive); }
.status-completed { color: var(--status-completed); }
.status-archived { color: var(--status-archived); }

.actions-section {
    border-top: 1px solid var(--border-color-light);
    padding-top: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}
