package by.algin.projectservice.controller;

import by.algin.dto.project.CreateInvitationRequest;
import by.algin.dto.project.InvitationResponse;
import by.algin.dto.response.ApiResponse;
import by.algin.dto.response.PagedResponse;
import by.algin.constants.CommonPathConstants;
import by.algin.projectservice.constants.ProjectMessageConstants;
import by.algin.projectservice.util.PaginationHelper;
import by.algin.util.SecurityUtils;
import by.algin.projectservice.service.ProjectInvitationService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(CommonPathConstants.API_PROJECTS_PATH)
@RequiredArgsConstructor
@Slf4j
public class ProjectInvitationController {

    private final ProjectInvitationService invitationService;
    private final PaginationHelper paginationHelper;

    @PostMapping(CommonPathConstants.API_PROJECTS_INVITATIONS)
    public ResponseEntity<InvitationResponse> createInvitation(@PathVariable Long projectId,
                                                              @Valid @RequestBody CreateInvitationRequest request) {
        log.info("Starting operation: createInvitation with params: {}", projectId);
        Long currentUserId = SecurityUtils.getCurrentUserIdOrThrow();
        log.debug("User {} performing operation: creating invitation", currentUserId);
        InvitationResponse response = invitationService.createInvitation(projectId, request, currentUserId);
        log.info("Operation createInvitation completed successfully. Result: {}", response.getId());
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @GetMapping(CommonPathConstants.API_PROJECTS_INVITATIONS)
    public ResponseEntity<PagedResponse<InvitationResponse>> getProjectInvitations(
            @PathVariable Long projectId,
            @RequestParam(required = false) Integer page,
            @RequestParam(required = false) Integer size,
            @RequestParam(required = false) String sortBy,
            @RequestParam(required = false) String sortDir) {

        Long currentUserId = SecurityUtils.getCurrentUserIdOrThrow();
        log.debug(ProjectMessageConstants.LOG_GETTING_INVITATIONS_BY_USER, projectId, currentUserId);
        log.info(ProjectMessageConstants.LOG_GETTING_INVITATIONS_WITH_PAGINATION,
                projectId, page, size, sortBy, sortDir);
        Page<InvitationResponse> invitations = invitationService.getProjectInvitations(
                projectId, currentUserId, paginationHelper.createPageable(page, size, sortBy, sortDir));
        PagedResponse<InvitationResponse> response = PaginationHelper.toPagedResponse(invitations);
        return ResponseEntity.ok(response);
    }

    @GetMapping(CommonPathConstants.API_INVITATIONS_BY_TOKEN)
    public ResponseEntity<InvitationResponse> getInvitationByToken(@PathVariable String token) {
        log.info(ProjectMessageConstants.LOG_GETTING_INVITATION_BY_TOKEN, token);
        InvitationResponse response = invitationService.getInvitationByToken(token);
        return ResponseEntity.ok(response);
    }

    @PostMapping(CommonPathConstants.API_INVITATIONS_ACCEPT)
    public ResponseEntity<ApiResponse<String>> acceptInvitation(@PathVariable String token) {
        log.info(ProjectMessageConstants.LOG_ACCEPTING_INVITATION, token);
        Long currentUserId = SecurityUtils.getCurrentUserIdOrThrow();
        log.debug(ProjectMessageConstants.LOG_USER_ACCEPTING_INVITATION, currentUserId, token);
        invitationService.acceptInvitation(token, currentUserId);
        log.info(ProjectMessageConstants.LOG_SUCCESSFULLY_ACCEPTED_INVITATION, token);
        return ResponseEntity.ok(ApiResponse.success(ProjectMessageConstants.INVITATION_ACCEPTED_SUCCESSFULLY));
    }

    @DeleteMapping(CommonPathConstants.API_PROJECTS_INVITATIONS_CANCEL)
    public ResponseEntity<ApiResponse<String>> cancelInvitation(@PathVariable Long projectId,
                                                       @PathVariable Long invitationId) {
        log.info(ProjectMessageConstants.LOG_CANCELING_INVITATION, invitationId, projectId);
        Long currentUserId = SecurityUtils.getCurrentUserIdOrThrow();
        log.debug(ProjectMessageConstants.LOG_USER_CANCELING_INVITATION, currentUserId, invitationId, projectId);
        invitationService.cancelInvitation(invitationId, currentUserId);
        log.info(ProjectMessageConstants.LOG_SUCCESSFULLY_CANCELED_INVITATION, invitationId, projectId);
        return ResponseEntity.ok(ApiResponse.success(ProjectMessageConstants.INVITATION_CANCELED_SUCCESSFULLY));
    }
}
