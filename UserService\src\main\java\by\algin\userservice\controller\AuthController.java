package by.algin.userservice.controller;

import by.algin.api.AuthApi;
import by.algin.dto.request.LoginRequest;
import by.algin.dto.request.RegisterRequest;
import by.algin.dto.request.TokenRefreshRequest;
import by.algin.dto.request.TokenValidationRequest;
import by.algin.dto.response.ApiResponse;
import by.algin.dto.response.AuthResponse;
import by.algin.dto.response.TokenValidationResponse;
import by.algin.dto.response.UserResponse;

import by.algin.userservice.constants.MessageConstants;
import by.algin.userservice.constants.PathConstants;
import by.algin.userservice.service.AuthService;
import by.algin.userservice.service.TokenValidationService;
import by.algin.userservice.service.UserService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping(PathConstants.API_AUTH_BASE)
@RequiredArgsConstructor
public class AuthController implements AuthApi {

    private final AuthService authService;
    private final UserService userService;
    private final TokenValidationService tokenValidationService;

    @Override
    @PostMapping("/register")
    public ApiResponse<UserResponse> register(@RequestBody RegisterRequest registerRequest) {
        log.info("Processing registration request for: {}", registerRequest.getUsername());
        ApiResponse<UserResponse> response = userService.registerUser(registerRequest);
        return response;
    }

    @Override
    @PostMapping("/login")
    public ApiResponse<AuthResponse> login(@RequestBody LoginRequest loginRequest) {
        log.info("Processing login request for: {}", loginRequest.getUsernameOrEmail());
        ApiResponse<AuthResponse> response = authService.login(loginRequest);
        return response;
    }

    @Override
    @PostMapping("/refresh-token")
    public ApiResponse<AuthResponse> refreshToken(@RequestParam("refreshToken") String refreshToken) {
        log.info("Processing token refresh request");
        TokenRefreshRequest request = new TokenRefreshRequest();
        request.setRefreshToken(refreshToken);
        ApiResponse<AuthResponse> response = authService.refreshToken(request);
        return response;
    }

    @Override
    @PostMapping("/validate-token")
    public ApiResponse<Boolean> validateToken(@RequestBody TokenValidationRequest tokenValidationRequest) {
        log.info("Processing token validation request");
        ApiResponse<TokenValidationResponse> validationResponse = authService.validateToken(tokenValidationRequest);
        if (validationResponse.isSuccess() && validationResponse.getData() != null) {
            return ApiResponse.success(validationResponse.getData().isValid());
        } else {
            return ApiResponse.success(false);
        }
    }

    @GetMapping(PathConstants.VALIDATE)
    public ResponseEntity<Boolean> validateTokenSimple(@RequestHeader("Authorization") String authHeader) {
        boolean isValid = tokenValidationService.validateTokenSimple(authHeader);
        return ResponseEntity.ok(isValid);
    }

    @GetMapping(PathConstants.VALIDATE + "/detailed")
    public ResponseEntity<ApiResponse<TokenValidationResponse>> validateTokenDetailed(@RequestHeader("Authorization") String authHeader) {
        ApiResponse<TokenValidationResponse> response = tokenValidationService.validateTokenFromHeader(authHeader);

        if (response.isSuccess()) {
            return ResponseEntity.ok(response);
        } else {
            return ResponseEntity.status(401).body(response);
        }
    }

    @Override
    @PostMapping("/confirm")
    public ApiResponse<String> confirmAccount(@RequestParam("token") String token) {
        log.debug("Processing account confirmation request");
        ApiResponse<String> response = userService.confirmAccount(token);
        return response;
    }

    @Override
    @PostMapping("/resend-confirmation")
    public ApiResponse<String> resendConfirmation(@RequestParam("email") String email) {
        log.info("Processing resend confirmation request for: {}", email);
        userService.resendConfirmationToken(email);
        ApiResponse<String> response = ApiResponse.success(MessageConstants.CONFIRMATION_EMAIL_RESENT, null);
        return response;
    }

    @Override
    @GetMapping("/email-by-token")
    public ApiResponse<String> getEmailByToken(@RequestParam("token") String token) {
        log.debug("Processing email retrieval request");
        String email = userService.getUserEmailByToken(token);
        ApiResponse<String> response = ApiResponse.success("Email retrieved successfully", email);
        return response;
    }
}