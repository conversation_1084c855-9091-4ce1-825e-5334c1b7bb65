package by.algin.projectservice.service;

import by.algin.dto.project.AddProjectMemberRequest;
import by.algin.dto.project.ProjectMemberResponse;
import by.algin.dto.project.UpdateProjectMemberRoleRequest;
import by.algin.dto.response.PagedResponse;
import by.algin.dto.response.UserResponse;
import by.algin.projectservice.entity.Project;
import by.algin.projectservice.entity.ProjectMember;
import by.algin.projectservice.entity.ProjectRole;
import by.algin.projectservice.exception.ProjectMemberNotFoundException;
import by.algin.projectservice.exception.ProjectNotFoundException;
import by.algin.projectservice.repository.ProjectMemberRepository;
import by.algin.projectservice.repository.ProjectRepository;
import by.algin.projectservice.util.PaginationHelper;
import by.algin.projectservice.util.UserBaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProjectMemberService {

    private final ProjectRepository projectRepository;
    private final ProjectMemberRepository projectMemberRepository;
    private final ProjectPermissionService permissionService;
    private final ProjectBusinessRulesService businessRulesService;
    private final ProjectRoleService roleService;
    private final MetricsService metricsService;
    private final UserBaseService userBaseService;

    @Transactional
    public ProjectMemberResponse addProjectMember(Long projectId, AddProjectMemberRequest request, Long currentUserId) {
        log.debug("Adding user {} to project {} with role {} by user {}",
                request.getUserId(), projectId, request.getRole(), currentUserId);

        Project project = getProjectOrThrow(projectId);
        validateProjectManagementAccess(project, currentUserId);

        Long userId = request.getUserId();
        by.algin.dto.project.ProjectRole requestRoleDto = request.getRole() != null ?
            request.getRole() : by.algin.dto.project.ProjectRole.DEVELOPER;
        ProjectRole role = roleService.getRoleByNameOrThrow(requestRoleDto.name());

        UserResponse user = userBaseService.getUserByIdOrThrow(userId);
        businessRulesService.validateMemberAddition(project, userId, role);

        ProjectMember member = createProjectMember(project, userId, role);
        member = projectMemberRepository.save(member);

        metricsService.recordMemberAddition(project.getId(), userId, requestRoleDto);
        log.debug("Successfully added user {} to project {}", request.getUserId(), projectId);

        return convertToProjectMemberResponse(member, user);
    }

    @Transactional
    public ProjectMemberResponse updateProjectMemberRole(Long projectId, Long userId,
                                                        UpdateProjectMemberRoleRequest request, Long currentUserId) {
        log.debug("Updating role of user {} in project {} to {} by user {}",
                userId, projectId, request.getRole(), currentUserId);

        Project project = getProjectOrThrow(projectId);
        validateProjectManagementAccess(project, currentUserId);

        ProjectMember member = getProjectMemberOrThrow(projectId, userId);
        by.algin.dto.project.ProjectRole newRoleDto = request.getRole();
        ProjectRole newRole = roleService.getRoleByNameOrThrow(newRoleDto.name());

        businessRulesService.validateMemberRoleChange(project, member, newRole, currentUserId);

        member.setRole(newRole);
        member = projectMemberRepository.save(member);

        UserResponse user = userBaseService.getUserByIdOrThrow(userId);
        log.info("Successfully updated role of user {} in project {}", userId, projectId);
        
        return convertToProjectMemberResponse(member, user);
    }

    @Transactional
    public void removeProjectMember(Long projectId, Long userId, Long currentUserId) {
        log.info("Removing user {} from project {} by user {}", userId, projectId, currentUserId);

        Project project = getProjectOrThrow(projectId);
        validateProjectManagementAccess(project, currentUserId);

        ProjectMember member = getProjectMemberOrThrow(projectId, userId);
        businessRulesService.validateMemberRemoval(member, currentUserId);

        by.algin.dto.project.ProjectRole memberRoleDto = 
            by.algin.dto.project.ProjectRole.valueOf(member.getRole().getRoleName());

        projectMemberRepository.delete(member);
        metricsService.recordMemberRemoval(project.getId(), userId, memberRoleDto);

        log.info("Successfully removed user {} from project {}", userId, projectId);
    }

    @Transactional(readOnly = true)
    public List<ProjectMemberResponse> getProjectMembers(Long projectId, Long currentUserId) {
        log.debug("Getting members of project {} for user {}", projectId, currentUserId);

        Project project = getProjectOrThrow(projectId);
        validateProjectAccess(project, currentUserId);

        List<ProjectMember> members = projectMemberRepository.findByProjectIdOrderByJoinedAtAsc(projectId);
        return convertMembersToResponses(members);
    }

    @Transactional(readOnly = true)
    public PagedResponse<ProjectMemberResponse> getProjectMembers(Long projectId, Long currentUserId, Pageable pageable) {
        log.info("Getting members for project {} with pagination: page={}, size={}",
                projectId, pageable.getPageNumber(), pageable.getPageSize());

        Project project = getProjectOrThrow(projectId);
        validateProjectAccess(project, currentUserId);

        Page<ProjectMember> membersPage = projectMemberRepository.findByProjectIdOrderByJoinedAtAsc(projectId, pageable);
        List<ProjectMember> members = membersPage.getContent();
        Map<Long, UserResponse> users = getUsersForMembers(members);

        Page<ProjectMemberResponse> responsePage = membersPage.map(member -> {
            UserResponse user = users.get(member.getUserId());
            if (user == null) {
                log.warn("User not found for member {}, user ID: {}", member.getId(), member.getUserId());
            }
            return convertToProjectMemberResponse(member, user);
        });

        return PaginationHelper.toPagedResponse(responsePage);
    }

    private Project getProjectOrThrow(Long projectId) {
        return projectRepository.findById(projectId)
                .orElseThrow(() -> new ProjectNotFoundException(projectId));
    }

    private ProjectMember getProjectMemberOrThrow(Long projectId, Long userId) {
        return projectMemberRepository.findByProjectIdAndUserId(projectId, userId)
                .orElseThrow(() -> new ProjectMemberNotFoundException(projectId, userId));
    }

    private void validateProjectAccess(Project project, Long userId) {
        if (!project.getOwnerId().equals(userId) &&
            !projectMemberRepository.existsByProjectIdAndUserId(project.getId(), userId)) {
            throw new by.algin.projectservice.exception.ProjectAccessDeniedException(userId, project.getId());
        }
    }

    private void validateProjectManagementAccess(Project project, Long userId) {
        if (!permissionService.canManageMembers(project.getId(), userId)) {
            throw new by.algin.projectservice.exception.ProjectAccessDeniedException(userId, project.getId());
        }
    }

    private ProjectMember createProjectMember(Project project, Long userId, ProjectRole role) {
        return ProjectMember.builder()
                .project(project)
                .userId(userId)
                .role(role)
                .build();
    }

    private List<ProjectMemberResponse> convertMembersToResponses(List<ProjectMember> members) {
        Map<Long, UserResponse> users = getUsersForMembers(members);
        return members.stream()
                .map(member -> convertToProjectMemberResponse(member, users.get(member.getUserId())))
                .collect(Collectors.toList());
    }

    private Map<Long, UserResponse> getUsersForMembers(List<ProjectMember> members) {
        List<Long> userIds = members.stream()
                .map(ProjectMember::getUserId)
                .collect(Collectors.toList());
        return userBaseService.getUsersByIdsWithFallback(userIds);
    }

    private ProjectMemberResponse convertToProjectMemberResponse(ProjectMember member, UserResponse user) {
        by.algin.dto.project.ProjectRole roleDto = 
            by.algin.dto.project.ProjectRole.valueOf(member.getRole().getRoleName());

        return ProjectMemberResponse.builder()
                .userId(member.getUserId())
                .username(user != null ? user.getUsername() : "Unknown User")
                .email(user != null ? user.getEmail() : null)
                .role(roleDto)
                .joinedAt(member.getJoinedAt())
                .build();
    }
}
