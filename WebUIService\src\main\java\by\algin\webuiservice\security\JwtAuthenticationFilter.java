package by.algin.webuiservice.security;

import by.algin.constants.CommonPathConstants;
import by.algin.constants.CommonTemplateConstants;
import by.algin.common.exception.TokenExpiredException;
import by.algin.webuiservice.service.JwtService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Set;

@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtService jwtService;
    private final UserDetailsService userDetailsService;

    private static final Set<String> PUBLIC_PATHS = Set.of(
            CommonTemplateConstants.CSS_RESOURCES,
            CommonTemplateConstants.JS_RESOURCES,
            CommonPathConstants.AUTH_LOGIN,
            CommonPathConstants.AUTH_REGISTER,
            CommonPathConstants.AUTH_REGISTRATION_SUCCESS,
            CommonPathConstants.AUTH_ACCOUNT_CONFIRMED,
            CommonPathConstants.AUTH_TOKEN_EXPIRED,
            CommonPathConstants.AUTH_RESEND_CONFIRMATION,
            CommonPathConstants.AUTH_CONFIRM + "/",
            CommonTemplateConstants.TEMPLATE_ERROR
    );

    @Override
    protected void doFilterInternal(
            HttpServletRequest request,
            HttpServletResponse response,
            FilterChain filterChain
    ) throws ServletException, IOException {
        try {
            if (shouldSkipAuthentication(request)) {
                filterChain.doFilter(request, response);
                return;
            }

            final String jwt = extractJwtFromRequest(request);
            if (jwt == null) {
                log.debug("No JWT token found in request");
                filterChain.doFilter(request, response);
                return;
            }

            final String username = jwtService.extractUsername(jwt);
            if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                UserDetails userDetails = this.userDetailsService.loadUserByUsername(username);
                
                if (jwtService.isTokenValid(jwt, userDetails)) {
                    UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
                            userDetails,
                            null,
                            userDetails.getAuthorities()
                    );
                    authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                    SecurityContextHolder.getContext().setAuthentication(authToken);
                    
                    jakarta.servlet.http.HttpSession session = request.getSession(true);
                    session.setAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY, 
                            SecurityContextHolder.getContext());
                    
                    log.debug("Authentication set for user: {}", username);
                } else {
                    log.warn("Invalid JWT token for user: {}", username);
                }
            }
        } catch (TokenExpiredException e) {
            log.warn("Token expired: {}", e.getMessage());
            response.sendRedirect(CommonPathConstants.AUTH_TOKEN_EXPIRED);
            return;
        } catch (Exception e) {
            log.error("Error processing JWT token: {}", e.getMessage());
        }
        
        filterChain.doFilter(request, response);
    }

    private boolean shouldSkipAuthentication(HttpServletRequest request) {
        String requestPath = request.getRequestURI();
        boolean shouldSkip = requestPath.equals("/") || PUBLIC_PATHS.stream().anyMatch(requestPath::startsWith);
        log.debug("Checking if should skip authentication for path: {}, result: {}", requestPath, shouldSkip);
        if (!shouldSkip) {
            log.debug("Public paths: {}", PUBLIC_PATHS);
        }
        return shouldSkip;
    }

    private String extractJwtFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            return authHeader.substring(7);
        }

        jakarta.servlet.http.Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (jakarta.servlet.http.Cookie cookie : cookies) {
                if (CommonPathConstants.JWT_COOKIE_NAME.equals(cookie.getName())) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }
}