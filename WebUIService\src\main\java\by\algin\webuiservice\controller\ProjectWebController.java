package by.algin.webuiservice.controller;

import by.algin.constants.CommonPathConstants;
import by.algin.constants.CommonTemplateConstants;
import by.algin.dto.project.*;
import by.algin.dto.response.UserResponse;
import by.algin.webuiservice.constants.PathConstants;
import by.algin.webuiservice.dto.ServiceResult;
import by.algin.webuiservice.service.ProjectWebService;
import by.algin.webuiservice.service.ProjectRoleWebService;
import by.algin.webuiservice.util.ControllerModelHelper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.Optional;

@Slf4j
@Controller
@RequestMapping(CommonPathConstants.PROJECTS_ENDPOINT)
@RequiredArgsConstructor
public class ProjectWebController {

    private final ProjectWebService projectWebService;
    private final ProjectRoleWebService projectRoleWebService;

    @ModelAttribute
    public void prepareProjectData(@PathVariable(required = false) Long projectId,
                                  @PathVariable(required = false) String projectIdString,
                                  Authentication authentication,
                                  Model model,
                                  HttpServletRequest request) {

        String requestURI = request.getRequestURI();
        if (shouldPrepareProjectData(requestURI)) {
            Long actualProjectId = determineProjectId(projectId, projectIdString);
            if (actualProjectId != null) {
                log.debug("Preparing project data for projectId: {}", actualProjectId);
                prepareProjectViewData(actualProjectId, authentication, model);
            }
        }
    }

    private boolean shouldPrepareProjectData(String requestURI) {
        return requestURI.matches(".*/projects/\\d+(/.*)?") &&
               !requestURI.contains("/delete") &&
               !requestURI.contains("/members/remove");
    }

    private Long determineProjectId(Long projectId, String projectIdString) {
        if (projectId != null) {
            return projectId;
        }
        if (projectIdString != null) {
            try {
                return Long.parseLong(projectIdString);
            } catch (NumberFormatException e) {
                log.warn("Invalid project ID format: {}", projectIdString);
                return null;
            }
        }
        return null;
    }

    private void prepareProjectViewData(Long projectId, Authentication authentication, Model model) {
        Optional<UserResponse> userOpt = projectWebService.getCurrentUser(authentication);
        if (userOpt.isEmpty()) {
            ControllerModelHelper.addErrorIfNotPresent(model, "User not found");
            return;
        }

        ServiceResult<ProjectWebService.ProjectViewData> result =
            projectWebService.getProjectViewData(projectId, userOpt.get());

        if (result.isSuccess() && result.getData() != null) {
            ProjectWebService.ProjectViewData data = result.getData();
            ControllerModelHelper.prepareProjectViewData(model,
                data.getProject(), data.getMembers(), data.getUser());
        } else {
            ControllerModelHelper.addErrorIfNotPresent(model,
                result.getErrorMessage() != null ? result.getErrorMessage() : "Failed to load project data");
        }
    }

    @ModelAttribute
    public void prepareRolesForMemberPages(HttpServletRequest request, Model model) {
        String requestURI = request.getRequestURI();
        if (requestURI.contains("/members")) {
            model.addAttribute("roles", projectRoleWebService.getAssignableRoles());
        }
    }

    @GetMapping(CommonPathConstants.CREATE_ENDPOINT)
    public String showCreateForm(Model model) {
        model.addAttribute("project", new CreateProjectRequest());
        return CommonTemplateConstants.TEMPLATE_PROJECTS_CREATE;
    }

    @PostMapping(CommonPathConstants.CREATE_ENDPOINT)
    public String createProject(@Valid @ModelAttribute("project") CreateProjectRequest request,
                               BindingResult result,
                               Authentication authentication,
                               Model model,
                               RedirectAttributes redirectAttributes) {

        if (result.hasErrors()) {
            return CommonTemplateConstants.TEMPLATE_PROJECTS_CREATE;
        }

        boolean created = projectWebService.createProject(request, authentication, model);
        if (created) {
            redirectAttributes.addFlashAttribute("success", "Project created successfully!");
            return "redirect:" + PathConstants.DASHBOARD;
        } else {
            return CommonTemplateConstants.TEMPLATE_PROJECTS_CREATE;
        }
    }

    @GetMapping(CommonPathConstants.PROJECT_ID_ENDPOINT)
    public String viewProject(@PathVariable Long projectId,
                             Authentication authentication,
                             Model model) {

        return CommonTemplateConstants.TEMPLATE_PROJECTS_VIEW;
    }

    @GetMapping(CommonPathConstants.PROJECT_EDIT_ENDPOINT)
    public String showEditForm(@PathVariable Long projectId,
                              Authentication authentication,
                              Model model) {

        model.addAttribute("updateRequest", new UpdateProjectRequest());

        return CommonTemplateConstants.TEMPLATE_PROJECTS_EDIT;
    }

    @PostMapping(CommonPathConstants.PROJECT_EDIT_ENDPOINT)
    public String updateProject(@PathVariable Long projectId,
                               @Valid @ModelAttribute("updateRequest") UpdateProjectRequest request,
                               BindingResult result,
                               Authentication authentication,
                               Model model,
                               RedirectAttributes redirectAttributes) {

        if (result.hasErrors()) {
            return CommonTemplateConstants.TEMPLATE_PROJECTS_EDIT;
        }

        boolean updated = projectWebService.updateProject(projectId, request, authentication, model);
        if (updated) {
            redirectAttributes.addFlashAttribute("success", "Project updated successfully!");
            return CommonTemplateConstants.REDIRECT_PROJECTS_VIEW + projectId;
        } else {
            return CommonTemplateConstants.TEMPLATE_PROJECTS_EDIT;
        }
    }

    @PostMapping(CommonPathConstants.PROJECT_DELETE_ENDPOINT)
    public String deleteProject(@PathVariable String projectId,
                               Authentication authentication,
                               RedirectAttributes redirectAttributes) {

        ServiceResult<?> result = projectWebService.deleteProjectWithResult(Long.parseLong(projectId), authentication);

        if (result.isSuccess()) {
            redirectAttributes.addFlashAttribute("success", result.getSuccessMessage());
        } else {
            redirectAttributes.addFlashAttribute("error", result.getErrorMessage());
        }

        return "redirect:" + PathConstants.DASHBOARD;
    }

    @GetMapping(CommonPathConstants.PROJECT_MEMBERS_ENDPOINT)
    public String viewProjectMembers(@PathVariable String projectId,
                                   Authentication authentication,
                                   Model model) {

        model.addAttribute("memberRequest", new AddProjectMemberRequest());

        return CommonTemplateConstants.TEMPLATE_PROJECTS_MEMBERS;
    }

    @PostMapping(CommonPathConstants.PROJECT_MEMBERS_ENDPOINT)
    public String addProjectMember(@PathVariable String projectId,
                                  @Valid @ModelAttribute("memberRequest") AddProjectMemberRequest request,
                                  BindingResult result,
                                  Authentication authentication,
                                  Model model,
                                  RedirectAttributes redirectAttributes) {

        if (result.hasErrors()) {
            return CommonTemplateConstants.TEMPLATE_PROJECTS_MEMBERS;
        }

        boolean added = projectWebService.addProjectMember(Long.parseLong(projectId), request, authentication, model);
        if (added) {
            redirectAttributes.addFlashAttribute("success", "Member added successfully!");
        } else {
            redirectAttributes.addFlashAttribute("error", "Failed to add member!");
        }

        return CommonTemplateConstants.REDIRECT_PROJECTS_VIEW + projectId + CommonTemplateConstants.REDIRECT_PROJECTS_MEMBERS;
    }

    @PostMapping(CommonPathConstants.PROJECT_MEMBERS_ENDPOINT + CommonPathConstants.MEMBERS_REMOVE_ENDPOINT)
    public String removeProjectMember(@PathVariable String projectId,
                                     @PathVariable String userId,
                                     Authentication authentication,
                                     RedirectAttributes redirectAttributes) {

        ServiceResult<?> result = projectWebService.removeProjectMemberWithResult(
            Long.parseLong(projectId), Long.parseLong(userId), authentication);

        if (result.isSuccess()) {
            redirectAttributes.addFlashAttribute("success", result.getSuccessMessage());
        } else {
            redirectAttributes.addFlashAttribute("error", result.getErrorMessage());
        }

        return CommonTemplateConstants.REDIRECT_PROJECTS_VIEW + projectId + CommonTemplateConstants.REDIRECT_PROJECTS_MEMBERS;
    }

    @ExceptionHandler(Exception.class)
    public String handleException(Exception e, RedirectAttributes redirectAttributes) {
        log.error("Error in ProjectWebController: ", e);
        redirectAttributes.addFlashAttribute("error", "An unexpected error occurred: " + e.getMessage());
        return "redirect:" + PathConstants.DASHBOARD;
    }
}
