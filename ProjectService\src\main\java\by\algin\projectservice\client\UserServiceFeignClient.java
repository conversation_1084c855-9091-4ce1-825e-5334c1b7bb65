package by.algin.projectservice.client;

import by.algin.constants.CommonServiceConstants;
import by.algin.constants.CommonPathConstants;
import by.algin.dto.response.ApiResponse;
import by.algin.dto.response.UserResponse;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(
    name = CommonServiceConstants.USER_SERVICE,
    contextId = CommonServiceConstants.USER_SERVICE_CONTEXT_ID,
    path = CommonPathConstants.API_PATH,
    configuration = by.algin.projectservice.config.UserServiceFeignConfig.class
)
public interface UserServiceFeignClient {

    @GetMapping(CommonPathConstants.API_USERS_BY_ID)
    UserResponse getUserById(@PathVariable(CommonPathConstants.PARAM_USER_ID) Long userId);

    @GetMapping(CommonPathConstants.API_USERS)
    ApiResponse<List<UserResponse>> getUsersByIds(@RequestParam("ids") List<Long> userIds);

    @GetMapping(CommonPathConstants.API_USERS_SEARCH)
    ApiResponse<UserResponse> searchUsers(@RequestParam(CommonPathConstants.PARAM_FIELD) String field,
                                          @RequestParam(CommonPathConstants.PARAM_VALUE) String value);

    @GetMapping(CommonPathConstants.API_USERS_EXISTS)
    Boolean userExists(@PathVariable(CommonPathConstants.PARAM_USER_ID) Long userId);

    @GetMapping(CommonPathConstants.API_AUTH_VALIDATE)
    Boolean validateToken(@RequestParam(CommonPathConstants.PARAM_TOKEN) String token);
}
