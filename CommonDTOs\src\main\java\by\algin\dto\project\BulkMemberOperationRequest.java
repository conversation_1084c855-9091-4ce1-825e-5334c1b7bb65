package by.algin.dto.project;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BulkMemberOperationRequest {

    public enum OperationType {
        UPDATE_ROLE,
        REMOVE_MEMBERS,
        ADD_MEMBERS
    }

    @NotNull(message = "Operation type is required")
    private OperationType operationType;

    @NotEmpty(message = "User IDs list cannot be empty")
    private List<Long> userIds;

    private ProjectRole newRole;
    
    private ProjectRole defaultRole;
}
