package by.algin.projectservice.service;

import by.algin.dto.project.ProjectRole;
import by.algin.projectservice.constants.ProjectMessageConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class MetricsService {

    public void recordProjectCreation(Long projectId, Long ownerId) {
        log.info(ProjectMessageConstants.LOG_PROJECT_CREATED, projectId, ownerId);
    }

    public void recordProjectDeletion(Long projectId, Long ownerId) {
        log.info(ProjectMessageConstants.LOG_PROJECT_DELETED, projectId, ownerId);
    }

    public void recordMemberAddition(Long projectId, Long userId, ProjectRole role) {
        log.info(ProjectMessageConstants.LOG_MEMBER_ADDED, projectId, userId, role);
    }

    public void recordMemberRemoval(Long projectId, Long userId, ProjectRole role) {
        log.info(ProjectMessageConstants.LOG_MEMBER_REMOVED, projectId, userId, role);
    }
}
