package by.algin.dto.project;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ProjectStatus {
    ACTIVE("Active"),
    INACTIVE("Inactive"),
    COMPLETED("Completed"),
    ARCHIVED("Archived");

    private final String displayName;

    @JsonValue
    public String toValue() {
        return this.name();
    }

    @JsonCreator
    public static ProjectStatus fromValue(String value) {
        return ProjectStatus.valueOf(value.toUpperCase());
    }
}