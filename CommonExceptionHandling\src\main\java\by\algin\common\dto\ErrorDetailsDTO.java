package by.algin.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ErrorDetailsDTO {

    private String errorCode;
    private String message;
    private Integer status;
    private String category;
    @Builder.Default
    private LocalDateTime timestamp = LocalDateTime.now();
    private String path;
    private String serviceName;
    private String reasonKey;
    private String exceptionType;
    private Map<String, Object> additionalDetails;
    private String originalMessage;
    private String traceId;
}
