package by.algin.projectservice.util;

import by.algin.dto.project.ProjectStatus;
import by.algin.projectservice.config.AppProperties;
import by.algin.projectservice.constants.ProjectMessageConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class ValidationUtils {

    private final AppProperties appProperties;

    public void validateProjectName(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException(ProjectMessageConstants.PROJECT_NAME_CANNOT_BE_EMPTY);
        }
        if (name.trim().length() < appProperties.getProject().getValidation().getMinNameLength()) {
            throw new IllegalArgumentException(ProjectMessageConstants.PROJECT_NAME_MIN_LENGTH +
                appProperties.getProject().getValidation().getMinNameLength() + ProjectMessageConstants.CHARACTERS_LONG);
        }
        if (name.trim().length() > appProperties.getProject().getValidation().getMaxNameLength()) {
            throw new IllegalArgumentException(ProjectMessageConstants.PROJECT_NAME_MAX_LENGTH +
                appProperties.getProject().getValidation().getMaxNameLength() + ProjectMessageConstants.CHARACTERS_LONG);
        }
    }

    public void validateProjectDescription(String description) {
        if (description != null && description.length() > appProperties.getProject().getValidation().getMaxDescriptionLength()) {
            throw new IllegalArgumentException(ProjectMessageConstants.PROJECT_DESCRIPTION_MAX_LENGTH +
                appProperties.getProject().getValidation().getMaxDescriptionLength() + ProjectMessageConstants.CHARACTERS);
        }
    }

    public void validateEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            throw new IllegalArgumentException(ProjectMessageConstants.EMAIL_CANNOT_BE_EMPTY);
        }
        if (!email.contains("@") || !email.contains(".")) {
            throw new IllegalArgumentException(ProjectMessageConstants.INVALID_EMAIL_FORMAT);
        }
    }

    public void validatePagination(int page, int size) {
        if (page < 0) {
            throw new IllegalArgumentException(ProjectMessageConstants.PAGE_NUMBER_CANNOT_BE_NEGATIVE);
        }
        if (size <= 0) {
            throw new IllegalArgumentException(ProjectMessageConstants.PAGE_SIZE_MUST_BE_POSITIVE);
        }
        if (size > appProperties.getPagination().getMaxPageSize()) {
            throw new IllegalArgumentException(ProjectMessageConstants.PAGE_SIZE_CANNOT_EXCEED + appProperties.getPagination().getMaxPageSize());
        }
    }

    public void validateId(Long id, String fieldName) {
        if (id == null) {
            throw new IllegalArgumentException(fieldName + ProjectMessageConstants.FIELD_CANNOT_BE_NULL);
        }
        if (id <= 0) {
            throw new IllegalArgumentException(fieldName + ProjectMessageConstants.FIELD_MUST_BE_POSITIVE);
        }
    }

    public void validateProjectId(Long projectId) {
        validateId(projectId, "Project ID");
    }

    public void validateUserId(Long userId) {
        validateId(userId, "User ID");
    }

    public void validateProjectStatus(ProjectStatus status, String fieldName) {
        if (status == null) {
            throw new IllegalArgumentException(fieldName + " status cannot be null");
        }
    }

    public void validateStatusTransition(ProjectStatus fromStatus, ProjectStatus toStatus) {
        validateProjectStatus(fromStatus, "From");
        validateProjectStatus(toStatus, "To");

        if (fromStatus == toStatus) {
            throw new IllegalArgumentException("From and To statuses cannot be the same");
        }
    }

    public void validateUserRole(String userRole) {
        if (userRole != null && userRole.trim().isEmpty()) {
            throw new IllegalArgumentException("User role cannot be empty if provided");
        }
    }
}
