@echo off
echo ========================================
echo ОБНОВЛЕНИЕ ЗАВИСИМОСТЕЙ МИКРОСЕРВИСОВ
echo ========================================

echo.
echo [1/4] Пересборка CommonDTOs...
cd "s:\Deep Dark Fantasies\Java\3Task\TaskHelper\CommonDTOs"
call mvn clean install -DskipTests
if %ERRORLEVEL% neq 0 (
    echo ОШИБКА: Не удалось собрать CommonDTOs
    pause
    exit /b 1
)
echo ✅ CommonDTOs собран успешно

echo.
echo [2/4] Обновление ProjectService...
cd "s:\Deep Dark Fantasies\Java\3Task\TaskHelper\ProjectService"
call mvn clean compile -DskipTests
if %ERRORLEVEL% neq 0 (
    echo ОШИБКА: Не удалось обновить ProjectService
    pause
    exit /b 1
)
echo ✅ ProjectService обновлен успешно

echo.
echo [3/4] Обновление WebUIService...
cd "s:\Deep Dark Fantasies\Java\3Task\TaskHelper\WebUIService"
call mvn clean compile -DskipTests
if %ERRORLEVEL% neq 0 (
    echo ОШИБКА: Не удалось обновить WebUIService
    pause
    exit /b 1
)
echo ✅ WebUIService обновлен успешно

echo.
echo [4/4] Обновление UserService...
cd "s:\Deep Dark Fantasies\Java\3Task\TaskHelper\UserService"
call mvn clean compile -DskipTests
if %ERRORLEVEL% neq 0 (
    echo ОШИБКА: Не удалось обновить UserService
    pause
    exit /b 1
)
echo ✅ UserService обновлен успешно

echo.
echo ========================================
echo ✅ ВСЕ ЗАВИСИМОСТИ ОБНОВЛЕНЫ УСПЕШНО!
echo ========================================
echo.
echo Теперь можно продолжить внедрение...
pause
