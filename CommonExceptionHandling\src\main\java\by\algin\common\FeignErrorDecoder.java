package by.algin.common;

import by.algin.dto.response.ApiResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
@ConditionalOnClass(ErrorDecoder.class)
@RequiredArgsConstructor
public class FeignErrorDecoder implements ErrorDecoder {

    private final ObjectMapper objectMapper;

    @Override
    public Exception decode(String methodKey, Response response) {
        String serviceName = extractServiceName(methodKey);

        try {
            String responseBody = getResponseBody(response);

            if (responseBody != null && !responseBody.trim().isEmpty()) {
                try {
                    ApiResponse<?> apiResponse = objectMapper.readValue(responseBody, ApiResponse.class);

                    String errorCode = apiResponse.getErrorCode() != null ? apiResponse.getErrorCode() : "UNKNOWN_ERROR";
                    String message = apiResponse.getMessage() != null ? apiResponse.getMessage() : "Service error";

                    log.warn("Service call failed: {} (status: {}, code: {}, message: {})",
                            serviceName, response.status(), errorCode, message);

                    return new ServiceCallException(errorCode, message, serviceName, response.status());

                } catch (Exception e) {
                    log.debug("Failed to parse as ApiResponse: {}", e.getMessage());
                }
            }

            String message = getDefaultMessage(response.status());
            String code = getDefaultCode(response.status());

            log.warn("Service call failed: {} (status: {}, fallback error)", serviceName, response.status());

            return new ServiceCallException(code, message, serviceName, response.status());

        } catch (Exception e) {
            log.error("Error decoding Feign response from {}: {}", serviceName, e.getMessage());
            return new ServiceCallException("DECODE_ERROR", "Failed to decode error response", serviceName, response.status());
        }
    }



    private String extractServiceName(String methodKey) {
        if (methodKey != null && methodKey.contains("#")) {
            String className = methodKey.substring(0, methodKey.indexOf("#"));
            if (className.contains(".")) {
                className = className.substring(className.lastIndexOf(".") + 1);
            }
            return className.replace("Client", "").replace("Service", "").toUpperCase();
        }
        return "UNKNOWN-SERVICE";
    }

    private String getResponseBody(Response response) {
        try {
            if (response.body() != null) {
                InputStream inputStream = response.body().asInputStream();
                return new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
            }
        } catch (Exception e) {
            log.warn("Failed to read response body: {}", e.getMessage());
        }
        return null;
    }

    private String getDefaultMessage(int status) {
        return switch (status) {
            case 400 -> "Bad request";
            case 401 -> "Unauthorized";
            case 403 -> "Forbidden";
            case 404 -> "Not found";
            case 500 -> "Internal server error";
            case 503 -> "Service unavailable";
            default -> "Service error";
        };
    }

    private String getDefaultCode(int status) {
        return switch (status) {
            case 400 -> "BAD_REQUEST";
            case 401 -> "UNAUTHORIZED";
            case 403 -> "FORBIDDEN";
            case 404 -> "NOT_FOUND";
            case 500 -> "INTERNAL_ERROR";
            case 503 -> "SERVICE_UNAVAILABLE";
            default -> "SERVICE_ERROR";
        };
    }

    public static class ServiceCallException extends RuntimeException {
        private final String errorCode;
        private final String serviceName;
        private final int httpStatus;

        public ServiceCallException(String errorCode, String message, String serviceName, int httpStatus) {
            super(message);
            this.errorCode = errorCode;
            this.serviceName = serviceName;
            this.httpStatus = httpStatus;
        }

        public String getErrorCode() { return errorCode; }
        public String getServiceName() { return serviceName; }
        public int getHttpStatus() { return httpStatus; }
    }
}
