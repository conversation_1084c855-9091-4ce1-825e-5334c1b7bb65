# Spring Boot Configuration
spring.application.name=USER-SERVICE
server.port=8081

# Eureka Client Configuration
eureka.client.serviceUrl.defaultZone=http://localhost:8761/eureka/
eureka.instance.hostname=localhost

# Database Configuration
spring.datasource.url=************************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.open-in-view=false

# Flyway Configuration - ОТКЛЮЧЕН, используем принудительное выполнение
spring.flyway.enabled=false
spring.flyway.clean-disabled=false

# Mail Configuration
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=gwvn nmgo ynft yacb 
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# Application Properties
app.security.jwt.secret-key=${JWT_SECRET_KEY:mySecretKey123456789012345678901234567890}
app.security.jwt.access-token-expiration=${JWT_ACCESS_TOKEN_EXPIRATION_SECONDS:3600000}
app.security.jwt.refresh-token-expiration=${JWT_REFRESH_TOKEN_EXPIRATION_SECONDS:********}

app.confirmation.expirationMinutes=30
app.confirmation.url=http://localhost:8082/auth/confirm

app.mail.registrationSubject=Account Confirmation
app.mail.registrationBody=Please click the following link to confirm your account: {url}

app.rememberMe.key=myRememberMeKey123456789012345678901234567890
app.rememberMe.tokenValiditySeconds=1209600

app.rateLimit.resendConfirmation.maxRequests=3
app.rateLimit.resendConfirmation.windowSeconds=3600

# Default admin user
app.defaultAdmin.username=admin
app.defaultAdmin.email=<EMAIL>
app.defaultAdmin.password=11

# Database migration
app.database.migration.location=classpath:db/migration