package by.algin.projectservice.exception;

import lombok.Getter;

@Getter
public class JwtAuthenticationException extends RuntimeException {

    private final ProjectErrorCodes errorCode;
    private final String token;

    public JwtAuthenticationException(ProjectErrorCodes errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.token = null;
    }
}
