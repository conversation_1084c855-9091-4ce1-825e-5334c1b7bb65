package by.algin.projectservice.service;

import by.algin.projectservice.config.AppProperties;
import by.algin.projectservice.constants.ProjectMessageConstants;
import by.algin.projectservice.exception.BusinessRuleViolationException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ProjectValidationService {

    private final AppProperties appProperties;

    public void validateProjectName(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new BusinessRuleViolationException(ProjectMessageConstants.PROJECT_NAME_CANNOT_BE_EMPTY);
        }
        
        int minLength = appProperties.getProject().getValidation().getMinNameLength();
        int maxLength = appProperties.getProject().getValidation().getMaxNameLength();

        if (name.length() < minLength) {
            throw new BusinessRuleViolationException(
                String.format("Project name must be at least %d characters long", minLength));
        }
        
        if (name.length() > maxLength) {
            throw new BusinessRuleViolationException(
                String.format("Project name cannot exceed %d characters", maxLength));
        }
    }

    public void validateProjectDescription(String description) {
        if (description != null) {
            int maxLength = appProperties.getProject().getValidation().getMaxDescriptionLength();
            if (description.length() > maxLength) {
                throw new BusinessRuleViolationException(
                    String.format("Project description cannot exceed %d characters", maxLength));
            }
        }
    }

    public void validateUserProjectLimit(int currentProjectCount, Long userId) {
        int maxProjects = appProperties.getProject().getLimits().getMaxProjectsPerUser();
        if (currentProjectCount >= maxProjects) {
            throw new BusinessRuleViolationException(
                String.format("User cannot have more than %d projects", maxProjects));
        }
    }

    public void validateProjectMemberLimit(int currentMemberCount) {
        int maxMembers = appProperties.getProject().getLimits().getMaxMembersPerProject();
        if (currentMemberCount >= maxMembers) {
            throw new BusinessRuleViolationException(
                String.format("Project cannot have more than %d members", maxMembers));
        }
    }

    public void validateMemberRole(by.algin.projectservice.entity.ProjectRole role) {
        if (role == null) {
            throw new BusinessRuleViolationException(ProjectMessageConstants.MEMBER_ROLE_CANNOT_BE_NULL);
        }
    }
}
