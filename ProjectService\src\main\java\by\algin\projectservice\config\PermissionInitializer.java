package by.algin.projectservice.config;

import by.algin.constants.CommonProjectConstants;
import by.algin.projectservice.entity.ProjectRole;
import by.algin.projectservice.entity.ProjectRolePermission;
import by.algin.projectservice.repository.ProjectRolePermissionRepository;
import by.algin.projectservice.repository.ProjectRoleRepository;
import by.algin.projectservice.service.RoleConfigurationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
public class PermissionInitializer {

    private final ProjectRoleRepository roleRepository;
    private final ProjectRolePermissionRepository permissionRepository;
    private final RoleConfigurationService roleConfigurationService;

    public void initializePermissions() {
        log.info("Initializing role permissions");

        if (!roleConfigurationService.getAvailableRoles().isEmpty()) {
            log.info("Configuration-based permissions detected, skipping legacy initialization");
            return;
        }

        log.info("No configuration-based permissions found, using legacy initialization");

        ProjectRole ownerRole = getRoleOrThrow(CommonProjectConstants.ROLE_OWNER);
        ProjectRole managerRole = getRoleOrThrow(CommonProjectConstants.ROLE_MANAGER);
        ProjectRole developerRole = getRoleOrThrow(CommonProjectConstants.ROLE_DEVELOPER);
        ProjectRole viewerRole = getRoleOrThrow(CommonProjectConstants.ROLE_VIEWER);

        createOwnerPermissions(ownerRole);
        createManagerPermissions(managerRole);
        createDeveloperPermissions(developerRole);
        createViewerPermissions(viewerRole);

        log.info("Role permissions initialization completed");
    }

    private void createOwnerPermissions(ProjectRole ownerRole) {
        List<String> ownerPermissions = List.of(
            CommonProjectConstants.PROJECT_VIEW,
            CommonProjectConstants.PROJECT_EDIT,
            CommonProjectConstants.PROJECT_DELETE,
            CommonProjectConstants.PROJECT_ARCHIVE,
            CommonProjectConstants.PROJECT_STATUS_CHANGE,
            CommonProjectConstants.MEMBER_VIEW,
            CommonProjectConstants.MEMBER_ADD,
            CommonProjectConstants.MEMBER_REMOVE,
            CommonProjectConstants.MEMBER_ROLE_CHANGE,
            CommonProjectConstants.INVITATION_CREATE,
            CommonProjectConstants.INVITATION_CANCEL,
            CommonProjectConstants.INVITATION_VIEW,
            CommonProjectConstants.SETTINGS_VIEW,
            CommonProjectConstants.SETTINGS_EDIT,
            CommonProjectConstants.ROLE_ASSIGN_OWNER,
            CommonProjectConstants.ROLE_ASSIGN_MANAGER,
            CommonProjectConstants.ROLE_ASSIGN_DEVELOPER,
            CommonProjectConstants.AUDIT_VIEW,
            CommonProjectConstants.METRICS_VIEW
        );

        createPermissionsForRole(ownerRole, ownerPermissions);
    }

    private void createManagerPermissions(ProjectRole managerRole) {
        List<String> managerPermissions = List.of(
            CommonProjectConstants.PROJECT_VIEW,
            CommonProjectConstants.PROJECT_EDIT,
            CommonProjectConstants.PROJECT_ARCHIVE,
            CommonProjectConstants.PROJECT_STATUS_CHANGE,
            CommonProjectConstants.MEMBER_VIEW,
            CommonProjectConstants.MEMBER_ADD,
            CommonProjectConstants.MEMBER_REMOVE,
            CommonProjectConstants.MEMBER_ROLE_CHANGE,
            CommonProjectConstants.INVITATION_CREATE,
            CommonProjectConstants.INVITATION_CANCEL,
            CommonProjectConstants.INVITATION_VIEW,
            CommonProjectConstants.SETTINGS_VIEW,
            CommonProjectConstants.SETTINGS_EDIT,
            CommonProjectConstants.ROLE_ASSIGN_DEVELOPER,
            CommonProjectConstants.ROLE_ASSIGN_VIEWER,
            CommonProjectConstants.METRICS_VIEW
        );

        createPermissionsForRole(managerRole, managerPermissions);
    }

    private void createDeveloperPermissions(ProjectRole developerRole) {
        List<String> developerPermissions = List.of(
            CommonProjectConstants.PROJECT_VIEW,
            CommonProjectConstants.MEMBER_VIEW,
            CommonProjectConstants.INVITATION_VIEW,
            CommonProjectConstants.SETTINGS_VIEW
        );

        createPermissionsForRole(developerRole, developerPermissions);
    }

    private void createViewerPermissions(ProjectRole viewerRole) {
        List<String> viewerPermissions = List.of(
            CommonProjectConstants.PROJECT_VIEW,
            CommonProjectConstants.MEMBER_VIEW,
            CommonProjectConstants.SETTINGS_VIEW
        );

        createPermissionsForRole(viewerRole, viewerPermissions);
    }

    private void createPermissionsForRole(ProjectRole role, List<String> permissions) {
        log.debug("Creating {} permissions for role {}", permissions.size(), role.getRoleName());
        
        for (String permission : permissions) {
            createPermissionIfNotExists(role, permission);
        }
    }

    private void createPermissionIfNotExists(ProjectRole role, String permissionName) {
        Optional<ProjectRolePermission> existingPermission =
            permissionRepository.findByRoleIdAndPermissionName(role.getId(), permissionName);

        if (existingPermission.isEmpty()) {
            ProjectRolePermission permission = ProjectRolePermission.builder()
                .role(role)
                .permissionName(permissionName)
                .isGranted(true)
                .createdBy("SYSTEM")
                .build();

            permissionRepository.save(permission);
            log.debug("Created permission {} for role {}", permissionName, role.getRoleName());
        }
    }

    private ProjectRole getRoleOrThrow(String roleName) {
        return roleRepository.findByRoleNameIgnoreCase(roleName)
                .orElseThrow(() -> new IllegalStateException("Role " + roleName + " not found. Initialize roles first."));
    }
}
