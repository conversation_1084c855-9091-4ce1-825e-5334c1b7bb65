package by.algin.projectservice.entity;

import by.algin.dto.project.InvitationResponse;
import by.algin.dto.project.InvitationStatus;
import by.algin.dto.project.ProjectRole;
import by.algin.dto.response.UserResponse;
import by.algin.projectservice.entity.Project;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

@Entity
@Table(name = "project_invitations")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProjectInvitation {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", nullable = false)
    private Project project;

    @Column(name = "invited_by_user_id", nullable = false)
    private Long invitedByUserId;

    @Column(name = "invited_email")
    private String invitedEmail;

    @Column(name = "accepted_email")
    private String acceptedEmail;

    @Enumerated(EnumType.STRING)
    @Column(name = "role", nullable = false)
    private ProjectRole role;

    @Column(name = "token", unique = true, nullable = false)
    private String token;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    @Builder.Default
    private InvitationStatus status = InvitationStatus.PENDING;

    @Column(name = "message")
    private String message;

    @Column(name = "expires_at", nullable = false)
    private LocalDateTime expiresAt;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "accepted_at")
    private LocalDateTime acceptedAt;

    @Column(name = "accepted_by_user_id")
    private Long acceptedByUserId;

    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiresAt);
    }

    public boolean canBeAccepted() {
        return status == InvitationStatus.PENDING && !isExpired();
    }

    public InvitationResponse toResponse(UserResponse invitedBy, String invitationUrl) {
        return InvitationResponse.builder()
                .id(id)
                .projectId(project.getId())
                .projectName(project.getName())
                .invitedEmail(invitedEmail)
                .acceptedEmail(acceptedEmail)
                .role(role)
                .token(token)
                .status(status)
                .invitedByUsername(invitedBy != null ? invitedBy.getUsername() : "Unknown User")
                .invitedByEmail(invitedBy != null ? invitedBy.getEmail() : null)
                .message(message)
                .expiresAt(expiresAt)
                .createdAt(createdAt)
                .acceptedAt(acceptedAt)
                .invitationUrl(invitationUrl)
                .build();
    }
}
