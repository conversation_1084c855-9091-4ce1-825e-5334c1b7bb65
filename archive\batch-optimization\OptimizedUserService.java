package by.algin.userservice.service;

import by.algin.dto.response.UserResponse;
import by.algin.userservice.config.BatchConfig;
import by.algin.userservice.exception.BatchSizeLimitExceededException;
import by.algin.userservice.mapper.UserMapper;
import by.algin.userservice.entity.User;
import by.algin.userservice.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Optimized UserService with batch processing, caching and performance improvements
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OptimizedUserService {

    private final UserRepository userRepository;
    private final UserMapper userMapper;
    private final BatchConfig batchConfig;

    /**
     * Get multiple users by their IDs with size limits and optimization
     */
    @Transactional(readOnly = true)
    public List<UserResponse> getUsersByIds(List<Long> userIds) {
        log.info("Getting users by IDs: {} (count: {})", userIds, userIds != null ? userIds.size() : 0);

        if (userIds == null || userIds.isEmpty()) {
            return Collections.emptyList();
        }

        // Validate batch size
        if (userIds.size() > batchConfig.getMaxSize()) {
            throw new BatchSizeLimitExceededException(
                String.format("Batch size %d exceeds maximum allowed size %d", userIds.size(), batchConfig.getMaxSize())
            );
        }

        // Remove duplicates and null values
        Set<Long> uniqueUserIds = userIds.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (uniqueUserIds.isEmpty()) {
            return Collections.emptyList();
        }

        log.debug("Processing {} unique user IDs after deduplication", uniqueUserIds.size());

        // Use optimized repository method with IN clause
        List<User> users = userRepository.findAllByIdIn(new ArrayList<>(uniqueUserIds));
        
        // Convert to response DTOs
        List<UserResponse> userResponses = users.stream()
                .map(userMapper::toUserResponse)
                .collect(Collectors.toList());

        log.info("Successfully retrieved {} users out of {} requested", userResponses.size(), uniqueUserIds.size());
        
        // Log missing users for monitoring
        if (userResponses.size() < uniqueUserIds.size()) {
            Set<Long> foundIds = userResponses.stream()
                    .map(UserResponse::getId)
                    .collect(Collectors.toSet());
            Set<Long> missingIds = uniqueUserIds.stream()
                    .filter(id -> !foundIds.contains(id))
                    .collect(Collectors.toSet());
            log.warn("Missing users with IDs: {}", missingIds);
        }

        return userResponses;
    }

    /**
     * Get users by IDs in chunks for large requests
     */
    @Transactional(readOnly = true)
    public List<UserResponse> getUsersByIdsChunked(List<Long> userIds, int chunkSize) {
        if (userIds == null || userIds.isEmpty()) {
            return Collections.emptyList();
        }

        if (chunkSize <= 0 || chunkSize > batchConfig.getMaxSize()) {
            chunkSize = batchConfig.getDefaultChunkSize();
        }

        log.info("Processing {} user IDs in chunks of {}", userIds.size(), chunkSize);

        List<UserResponse> allUsers = new ArrayList<>();
        
        for (int i = 0; i < userIds.size(); i += chunkSize) {
            int endIndex = Math.min(i + chunkSize, userIds.size());
            List<Long> chunk = userIds.subList(i, endIndex);
            
            try {
                List<UserResponse> chunkUsers = getUsersByIds(chunk);
                allUsers.addAll(chunkUsers);
                log.debug("Processed chunk {}-{}: {} users found", i, endIndex, chunkUsers.size());
            } catch (Exception e) {
                log.error("Error processing chunk {}-{}: {}", i, endIndex, e.getMessage());
                // Continue with next chunk instead of failing completely
            }
        }

        log.info("Chunked processing completed: {} total users retrieved", allUsers.size());
        return allUsers;
    }

    /**
     * Get users by IDs with caching
     */
    @Cacheable(value = "userBatch", key = "#userIds.hashCode()",
               condition = "#userIds.size() <= @batchConfig.smallBatchThreshold",
               cacheManager = "batchCacheManager")
    @Transactional(readOnly = true)
    public List<UserResponse> getUsersByIdsCached(List<Long> userIds) {
        log.debug("Cache miss for user batch: {}", userIds);
        return getUsersByIds(userIds);
    }

    /**
     * Get users by IDs as Map for efficient lookup
     */
    @Transactional(readOnly = true)
    public Map<Long, UserResponse> getUsersByIdsAsMap(List<Long> userIds) {
        List<UserResponse> users = getUsersByIds(userIds);
        return users.stream()
                .collect(Collectors.toMap(UserResponse::getId, user -> user));
    }

    /**
     * Get users by IDs with pagination support
     */
    @Transactional(readOnly = true)
    public Page<UserResponse> getUsersByIdsPaginated(List<Long> userIds, Pageable pageable) {
        if (userIds == null || userIds.isEmpty()) {
            return Page.empty(pageable);
        }

        // Validate batch size
        if (userIds.size() > batchConfig.getMaxSize()) {
            throw new BatchSizeLimitExceededException(
                String.format("Batch size %d exceeds maximum allowed size %d", userIds.size(), batchConfig.getMaxSize())
            );
        }

        log.info("Getting paginated users by IDs: page={}, size={}, total_ids={}", 
                pageable.getPageNumber(), pageable.getPageSize(), userIds.size());

        // Remove duplicates
        Set<Long> uniqueUserIds = userIds.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // Use repository method with pagination
        Page<User> userPage = userRepository.findAllByIdInOrderById(new ArrayList<>(uniqueUserIds), pageable);
        
        // Convert to response DTOs
        Page<UserResponse> responsePage = userPage.map(userMapper::toUserResponse);

        log.info("Retrieved page {} of {} with {} users", 
                responsePage.getNumber(), responsePage.getTotalPages(), responsePage.getNumberOfElements());

        return responsePage;
    }

    /**
     * Check if users exist by IDs (optimized for existence checks)
     */
    @Cacheable(value = "userExistsBatch", key = "#userIds.hashCode()",
               cacheManager = "batchCacheManager")
    @Transactional(readOnly = true)
    public Map<Long, Boolean> checkUsersExist(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // Validate batch size
        if (userIds.size() > batchConfig.getMaxSize()) {
            throw new BatchSizeLimitExceededException(
                String.format("Batch size %d exceeds maximum allowed size %d", userIds.size(), batchConfig.getMaxSize())
            );
        }

        log.debug("Checking existence for {} user IDs", userIds.size());

        // Remove duplicates
        Set<Long> uniqueUserIds = userIds.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // Use optimized count query instead of full select
        List<Long> existingIds = userRepository.findExistingIds(new ArrayList<>(uniqueUserIds));
        Set<Long> existingIdSet = new HashSet<>(existingIds);

        // Build result map
        Map<Long, Boolean> result = uniqueUserIds.stream()
                .collect(Collectors.toMap(id -> id, existingIdSet::contains));

        log.debug("Existence check completed: {} existing out of {} checked", existingIds.size(), uniqueUserIds.size());
        return result;
    }

    /**
     * Get user statistics for batch operations
     */
    @Transactional(readOnly = true)
    public BatchStatistics getBatchStatistics(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return new BatchStatistics(0, 0, 0, 0);
        }

        int totalRequested = userIds.size();
        Set<Long> uniqueIds = userIds.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        int uniqueRequested = uniqueIds.size();
        int duplicates = totalRequested - uniqueRequested;

        List<Long> existingIds = userRepository.findExistingIds(new ArrayList<>(uniqueIds));
        int found = existingIds.size();

        return new BatchStatistics(totalRequested, uniqueRequested, duplicates, found);
    }

    /**
     * Statistics for batch operations
     */
    public static class BatchStatistics {
        public final int totalRequested;
        public final int uniqueRequested;
        public final int duplicates;
        public final int found;

        public BatchStatistics(int totalRequested, int uniqueRequested, int duplicates, int found) {
            this.totalRequested = totalRequested;
            this.uniqueRequested = uniqueRequested;
            this.duplicates = duplicates;
            this.found = found;
        }

        public int getMissing() {
            return uniqueRequested - found;
        }

        public double getFoundPercentage() {
            return uniqueRequested > 0 ? (double) found / uniqueRequested * 100 : 0;
        }

        @Override
        public String toString() {
            return String.format("BatchStats{total=%d, unique=%d, duplicates=%d, found=%d, missing=%d, found%%=%.1f}", 
                    totalRequested, uniqueRequested, duplicates, found, getMissing(), getFoundPercentage());
        }
    }
}
