package by.algin.webuiservice.util;

import by.algin.constants.CommonPathConstants;
import by.algin.util.JsonResponseUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Slf4j
@Component
public class ErrorResponseHandler {

    public boolean isApiRequest(HttpServletRequest request) {
        return request.getRequestURI().startsWith(CommonPathConstants.API_PATH);
    }

    public void handleAuthenticationError(HttpServletRequest request,
                                        HttpServletResponse response,
                                        String errorMessage,
                                        String redirectUrl) throws IOException {
        if (isApiRequest(request)) {
            log.debug("Handling authentication error for API request: {}", request.getRequestURI());
            JsonResponseUtil.writeErrorResponse(
                response,
                "Unauthorized",
                errorMessage,
                HttpServletResponse.SC_UNAUTHORIZED,
                request.getRequestURI()
            );
        } else {
            log.debug("Redirecting web request to: {}", redirectUrl);
            response.sendRedirect(redirectUrl);
        }
    }

    public void handleAccessDeniedError(HttpServletRequest request,
                                      HttpServletResponse response,
                                      String errorMessage,
                                      String redirectUrl) throws IOException {
        if (isApiRequest(request)) {
            log.debug("Handling access denied error for API request: {}", request.getRequestURI());
            JsonResponseUtil.writeErrorResponse(
                response,
                "Forbidden",
                errorMessage,
                HttpServletResponse.SC_FORBIDDEN,
                request.getRequestURI()
            );
        } else {
            log.debug("Redirecting web request to: {}", redirectUrl);
            response.sendRedirect(redirectUrl);
        }
    }

    public void handleGeneralError(HttpServletRequest request,
                                 HttpServletResponse response,
                                 String errorType,
                                 String errorMessage,
                                 int statusCode,
                                 String redirectUrl) throws IOException {
        if (isApiRequest(request)) {
            log.debug("Handling {} error for API request: {}", errorType, request.getRequestURI());
            JsonResponseUtil.writeErrorResponse(
                response,
                errorType,
                errorMessage,
                statusCode,
                request.getRequestURI()
            );
        } else {
            log.debug("Redirecting web request to: {}", redirectUrl);
            response.sendRedirect(redirectUrl);
        }
    }
}
