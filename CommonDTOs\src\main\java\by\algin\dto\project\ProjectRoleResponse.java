package by.algin.dto.project;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectRoleResponse {
    
    private String roleName;
    private String displayName;
    private String description;
    private Integer permissionLevel;
    private String colorCode;
    private String iconName;
    private Boolean isActive;
    private Boolean canBeAssigned;
    private Integer maxPerProject;
    private Boolean isSystemRole;
}
