package by.algin.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TokenValidationResponse {
    private boolean isValid;
    private Long userId;
    private String username;
    private String email;
    private Set<String> roles;
    private Map<String, Object> claims;
    @Builder.Default
    private String message = null;

    public static TokenValidationResponse invalid(String message) {
        return TokenValidationResponse.builder()
                .isValid(false)
                .message(message)
                .build();
    }

    public static TokenValidationResponse valid(Long userId, String username, 
                                              String email, Set<String> roles, 
                                              Map<String, Object> claims) {
        return TokenValidationResponse.builder()
                .isValid(true)
                .userId(userId)
                .username(username)
                .email(email)
                .roles(roles)
                .claims(claims)
                .build();
    }
}