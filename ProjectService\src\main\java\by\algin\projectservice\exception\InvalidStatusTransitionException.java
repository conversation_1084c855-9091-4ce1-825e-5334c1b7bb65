package by.algin.projectservice.exception;

import by.algin.dto.project.ProjectStatus;
import lombok.Getter;

@Getter
public class InvalidStatusTransitionException extends RuntimeException {

    private final ProjectStatus fromStatus;
    private final ProjectStatus toStatus;
    private final String userRole;

    public InvalidStatusTransitionException(ProjectStatus fromStatus, ProjectStatus toStatus, String userRole) {
        super(buildMessage(fromStatus, toStatus, userRole));
        this.fromStatus = fromStatus;
        this.toStatus = toStatus;
        this.userRole = userRole;
    }

    private static String buildMessage(ProjectStatus fromStatus, ProjectStatus toStatus, String userRole) {
        String roleInfo = userRole != null ? " for role " + userRole : "";
        return String.format("Invalid status transition from %s to %s%s", fromStatus, toStatus, roleInfo);
    }
}
