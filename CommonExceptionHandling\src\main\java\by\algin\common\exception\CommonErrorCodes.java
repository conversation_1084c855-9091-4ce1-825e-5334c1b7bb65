package by.algin.common.exception;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;

@Getter
@RequiredArgsConstructor
public enum CommonErrorCodes implements ApiErrorCode {
    
    INTERNAL_SERVER_ERROR("COMMON_001", "Internal server error", HttpStatus.INTERNAL_SERVER_ERROR, "SYSTEM"),
    SERVICE_UNAVAILABLE("COMMON_002", "Service temporarily unavailable", HttpStatus.SERVICE_UNAVAILABLE, "SYSTEM"),
    REQUEST_TIMEOUT("COMMON_003", "Request timeout", HttpStatus.REQUEST_TIMEOUT, "SYSTEM"),

    VALIDATION_FAILED("COMMON_100", "Validation failed", HttpStatus.BAD_REQUEST, "VALIDATION"),
    INVALID_INPUT("COMMON_101", "Invalid input data", HttpStatus.BAD_REQUEST, "VALIDATION"),
    MISSING_REQUIRED_FIELD("COMMON_102", "Required field is missing", HttpStatus.BAD_REQUEST, "VALIDATION"),
    INVALID_EMAIL_FORMAT("COMMON_103", "Invalid email format", HttpStatus.BAD_REQUEST, "VALIDATION"),
    PASSWORDS_DONT_MATCH("COMMON_104", "Passwords do not match", HttpStatus.BAD_REQUEST, "VALIDATION"),

    AUTHENTICATION_FAILED("COMMON_200", "Authentication failed", HttpStatus.UNAUTHORIZED, "AUTHENTICATION"),
    INVALID_CREDENTIALS("COMMON_201", "Invalid credentials", HttpStatus.UNAUTHORIZED, "AUTHENTICATION"),
    ACCESS_DENIED("COMMON_202", "Access denied", HttpStatus.FORBIDDEN, "AUTHORIZATION"),
    TOKEN_EXPIRED("COMMON_203", "Token has expired", HttpStatus.UNAUTHORIZED, "AUTHENTICATION"),
    INVALID_TOKEN("COMMON_204", "Invalid token", HttpStatus.UNAUTHORIZED, "AUTHENTICATION"),

    RESOURCE_NOT_FOUND("COMMON_300", "Resource not found", HttpStatus.NOT_FOUND, "RESOURCE"),
    RESOURCE_ALREADY_EXISTS("COMMON_301", "Resource already exists", HttpStatus.CONFLICT, "RESOURCE"),
    RESOURCE_LOCKED("COMMON_302", "Resource is locked", HttpStatus.LOCKED, "RESOURCE"),

    RATE_LIMIT_EXCEEDED("COMMON_400", "Rate limit exceeded", HttpStatus.TOO_MANY_REQUESTS, "RATE_LIMIT"),
    QUOTA_EXCEEDED("COMMON_401", "Quota exceeded", HttpStatus.FORBIDDEN, "RATE_LIMIT"),

    USER_NOT_FOUND("COMMON_500", "User not found", HttpStatus.NOT_FOUND, "USER"),
    EMAIL_ALREADY_EXISTS("COMMON_501", "Email already exists", HttpStatus.CONFLICT, "USER"),
    USERNAME_ALREADY_EXISTS("COMMON_502", "Username already exists", HttpStatus.CONFLICT, "USER"),
    ACCOUNT_DISABLED("COMMON_503", "Account is disabled", HttpStatus.FORBIDDEN, "USER");
    
    private final String code;
    private final String defaultMessage;
    private final HttpStatus httpStatus;
    private final String category;
}
