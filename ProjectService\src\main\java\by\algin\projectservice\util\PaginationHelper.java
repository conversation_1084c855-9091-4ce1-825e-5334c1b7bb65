package by.algin.projectservice.util;

import by.algin.dto.common.PaginationParams;
import by.algin.dto.response.PagedResponse;
import by.algin.projectservice.config.AppProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PaginationHelper {

    private final AppProperties appProperties;
    public PaginationParams createPaginationParams(Integer page, Integer size, String sortBy, String sortDir) {
        AppProperties.Pagination paginationConfig = appProperties.getPagination();

        Integer finalPage = page != null ? page : paginationConfig.getDefaultPage();
        Integer finalSize = size != null ? size : paginationConfig.getDefaultPageSize();
        String finalSortBy = sortBy != null ? sortBy : paginationConfig.getDefaultSortBy();
        String finalSortDir = sortDir != null ? sortDir : paginationConfig.getDefaultSortDirection();

        if (finalSize > paginationConfig.getMaxPageSize()) {
            finalSize = paginationConfig.getMaxPageSize();
        }

        return new PaginationParams(finalPage, finalSize, finalSortBy, finalSortDir);
    }

    public PaginationParams createMembersPaginationParams(Integer page, Integer size, String sortBy, String sortDir) {
        AppProperties.Pagination paginationConfig = appProperties.getPagination();
        AppProperties.Pagination.Members membersConfig = paginationConfig.getMembers();
        
        Integer finalPage = page != null ? page : paginationConfig.getDefaultPage();
        Integer finalSize = size != null ? size : paginationConfig.getDefaultPageSize();
        String finalSortBy = sortBy != null ? sortBy : membersConfig.getDefaultSortBy();
        String finalSortDir = sortDir != null ? sortDir : membersConfig.getDefaultSortDirection();
        
        if (finalSize > paginationConfig.getMaxPageSize()) {
            finalSize = paginationConfig.getMaxPageSize();
        }
        
        return new PaginationParams(finalPage, finalSize, finalSortBy, finalSortDir);
    }

    public Pageable toPageable(PaginationParams params) {
        return params.toPageable();
    }

    public Pageable createPageable(Integer page, Integer size, String sortBy, String sortDir) {
        PaginationParams params = createPaginationParams(page, size, sortBy, sortDir);
        return toPageable(params);
    }

    public Pageable createMembersPageable(Integer page, Integer size, String sortBy, String sortDir) {
        PaginationParams params = createMembersPaginationParams(page, size, sortBy, sortDir);
        return toPageable(params);
    }

    public PaginationContext createPaginationContext(Integer page, Integer size, String sortBy, String sortDir) {
        PaginationParams params = createPaginationParams(page, size, sortBy, sortDir);
        Pageable pageable = toPageable(params);
        return new PaginationContext(params, pageable);
    }

    public PaginationContext createMembersPaginationContext(Integer page, Integer size, String sortBy, String sortDir) {
        PaginationParams params = createMembersPaginationParams(page, size, sortBy, sortDir);
        Pageable pageable = toPageable(params);
        return new PaginationContext(params, pageable);
    }

    public static <T> PagedResponse<T> toPagedResponse(Page<T> page) {
        return PagedResponse.<T>builder()
                .content(page.getContent())
                .page(page.getNumber())
                .size(page.getSize())
                .totalElements(page.getTotalElements())
                .totalPages(page.getTotalPages())
                .first(page.isFirst())
                .last(page.isLast())
                .build();
    }

    public static <T> PagedResponseWithParams<T> toPagedResponseWithParams(Page<T> page, PaginationParams paginationParams) {
        PagedResponse<T> pagedResponse = toPagedResponse(page);
        return new PagedResponseWithParams<>(pagedResponse, paginationParams);
    }

    public static class PaginationContext {
        private final PaginationParams params;
        private final Pageable pageable;

        public PaginationContext(PaginationParams params, Pageable pageable) {
            this.params = params;
            this.pageable = pageable;
        }

        public PaginationParams getParams() {
            return params;
        }

        public Pageable getPageable() {
            return pageable;
        }
    }

    public static class PagedResponseWithParams<T> {
        private final PagedResponse<T> pagedResponse;
        private final PaginationParams paginationParams;

        public PagedResponseWithParams(PagedResponse<T> pagedResponse, PaginationParams paginationParams) {
            this.pagedResponse = pagedResponse;
            this.paginationParams = paginationParams;
        }

        public PagedResponse<T> getPagedResponse() {
            return pagedResponse;
        }

        public PaginationParams getPaginationParams() {
            return paginationParams;
        }
    }
}
