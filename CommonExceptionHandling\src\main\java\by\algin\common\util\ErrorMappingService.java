package by.algin.common.util;

import by.algin.common.exception.CommonErrorCodes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Сервис для мапинга ошибок между микросервисами
 * Обеспечивает единообразную обработку ошибок и их преобразование
 */
@Slf4j
@Service
public class ErrorMappingService {

    private final Map<String, ErrorMapping> errorMappings = new HashMap<>();

    public ErrorMappingService() {
        initializeDefaultMappings();
    }

    /**
     * Инициализация стандартных мапингов ошибок
     */
    private void initializeDefaultMappings() {
        // Мапинг для UserService ошибок
        addMapping("UserService", "UserNotFoundException", 
                  CommonErrorCodes.USER_NOT_FOUND.getCode(), 
                  "User not found", 404);
        
        addMapping("UserService", "EmailAlreadyExistsException", 
                  CommonErrorCodes.EMAIL_ALREADY_EXISTS.getCode(), 
                  "Email already exists", 409);
        
        addMapping("UserService", "UsernameAlreadyExistsException", 
                  CommonErrorCodes.USERNAME_ALREADY_EXISTS.getCode(), 
                  "Username already exists", 409);
        
        addMapping("UserService", "InvalidCredentialsException", 
                  CommonErrorCodes.INVALID_CREDENTIALS.getCode(), 
                  "Invalid credentials", 401);

        // Мапинг для ProjectService ошибок
        addMapping("ProjectService", "ProjectNotFoundException", 
                  "PROJECT_NOT_FOUND", 
                  "Project not found", 404);
        
        addMapping("ProjectService", "UserNotFoundException", 
                  CommonErrorCodes.USER_NOT_FOUND.getCode(), 
                  "User not found", 404);

        // Мапинг для общих ошибок
        addMapping("*", "ValidationException", 
                  CommonErrorCodes.VALIDATION_FAILED.getCode(), 
                  "Validation failed", 400);
        
        addMapping("*", "IllegalArgumentException", 
                  CommonErrorCodes.INVALID_INPUT.getCode(), 
                  "Invalid input", 400);

        log.info("Initialized {} error mappings", errorMappings.size());
    }

    /**
     * Добавляет новый мапинг ошибки
     */
    public void addMapping(String serviceName, String exceptionType, String errorCode, String message, int status) {
        String key = createMappingKey(serviceName, exceptionType);
        errorMappings.put(key, new ErrorMapping(errorCode, message, status));
        log.debug("Added error mapping: {} -> {}", key, errorCode);
    }

    /**
     * Мапит исключение в стандартный формат ошибки
     */
    public ErrorMapping mapError(Exception exception, String serviceName) {
        String exceptionType = exception.getClass().getSimpleName();
        
        // Сначала ищем специфичный для сервиса мапинг
        String serviceKey = createMappingKey(serviceName, exceptionType);
        ErrorMapping mapping = errorMappings.get(serviceKey);
        
        if (mapping == null) {
            // Затем ищем общий мапинг
            String generalKey = createMappingKey("*", exceptionType);
            mapping = errorMappings.get(generalKey);
        }
        
        if (mapping != null) {
            log.debug("Mapped exception {} from {} to error code {}", 
                     exceptionType, serviceName, mapping.getCode());
        } else {
            log.debug("No mapping found for exception {} from {}", exceptionType, serviceName);
        }
        
        return mapping;
    }

    /**
     * Создает ключ для мапинга
     */
    private String createMappingKey(String serviceName, String exceptionType) {
        return serviceName + ":" + exceptionType;
    }

    /**
     * Класс для хранения информации о мапинге ошибки
     */
    public static class ErrorMapping {
        private final String code;
        private final String message;
        private final int status;

        public ErrorMapping(String code, String message, int status) {
            this.code = code;
            this.message = message;
            this.status = status;
        }

        public String getCode() { return code; }
        public String getMessage() { return message; }
        public int getStatus() { return status; }
    }

    /**
     * Получает все доступные мапинги (для отладки)
     */
    public Map<String, ErrorMapping> getAllMappings() {
        return new HashMap<>(errorMappings);
    }

    /**
     * Очищает все мапинги
     */
    public void clearMappings() {
        errorMappings.clear();
        log.info("Cleared all error mappings");
    }
}
