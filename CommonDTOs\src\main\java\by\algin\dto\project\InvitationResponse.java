package by.algin.dto.project;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InvitationResponse {
    
    private Long id;
    private Long projectId;
    private String projectName;
    private String invitedEmail;
    private String acceptedEmail;
    private ProjectRole role;
    private String token;
    private InvitationStatus status;
    private String invitedByUsername;
    private String invitedByEmail;
    private String message;
    private LocalDateTime expiresAt;
    private LocalDateTime createdAt;
    private LocalDateTime acceptedAt;
    private String invitationUrl;
}
