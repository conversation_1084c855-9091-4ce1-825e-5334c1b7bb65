package by.algin.dto.project;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateInvitationRequest {
    
    @Email(message = "Invalid email format")
    private String email;
    
    @NotNull(message = "Role is required")
    private ProjectRole role;
    
    private String message;
}
