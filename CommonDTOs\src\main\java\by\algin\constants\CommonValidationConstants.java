package by.algin.constants;

public final class CommonValidationConstants {

    // User validation messages
    public static final String USERNAME_REQUIRED = "{validation.username.required}";
    public static final String USERNAME_SIZE = "{validation.username.size}";
    public static final String EMAIL_REQUIRED = "{validation.email.required}";
    public static final String EMAIL_FORMAT = "{validation.email.format}";
    public static final String PASSWORD_REQUIRED = "{validation.password.required}";
    public static final String PASSWORD_SIZE = "{validation.password.size}";
    public static final String CONFIRM_PASSWORD_REQUIRED = "{validation.confirmPassword.required}";

    // Token validation messages
    public static final String TOKEN_REQUIRED = "Token is required";
    public static final String REFRESH_TOKEN_REQUIRED = "Refresh token is required";

    // Project validation messages
    public static final String PROJECT_NAME_REQUIRED = "Project name is required";
    public static final String PROJECT_NAME_SIZE = "Project name must be between 2 and 100 characters";
    public static final String PROJECT_DESCRIPTION_SIZE = "Description cannot exceed 500 characters";
    public static final String PROJECT_OWNER_REQUIRED = "Owner ID is required";
    public static final String PROJECT_STATUS_REQUIRED = "Project status is required";

    // Member validation messages
    public static final String MEMBER_ROLE_REQUIRED = "Member role is required";
    public static final String MEMBER_USER_ID_REQUIRED = "Member user ID is required";

    // Invitation validation messages
    public static final String INVITATION_EMAIL_REQUIRED = "Invitation email is required";
    public static final String INVITATION_ROLE_REQUIRED = "Invitation role is required";
    public static final String INVITATION_TOKEN_REQUIRED = "Invitation token is required";

    // User validation lengths
    public static final int USERNAME_MIN_LENGTH = 3;
    public static final int USERNAME_MAX_LENGTH = 50;
    public static final int PASSWORD_MIN_LENGTH = 6;

    // Project validation lengths
    public static final int PROJECT_NAME_MIN_LENGTH = 2;
    public static final int PROJECT_NAME_MAX_LENGTH = 100;
    public static final int PROJECT_DESCRIPTION_MAX_LENGTH = 500;

    // Invitation validation lengths
    public static final int INVITATION_TOKEN_LENGTH = 32;
    public static final int INVITATION_EXPIRY_HOURS = 24;

    // Pagination validation
    public static final int MIN_PAGE_NUMBER = 0;
    public static final int MIN_PAGE_SIZE = 1;
    public static final int MAX_PAGE_SIZE = 100;

    // ID validation
    public static final String ID_REQUIRED = "ID is required";
    public static final String ID_POSITIVE = "ID must be positive";

    // Generic validation messages
    public static final String FIELD_REQUIRED = "This field is required";
    public static final String INVALID_FORMAT = "Invalid format";
    public static final String VALUE_TOO_LONG = "Value is too long";
    public static final String VALUE_TOO_SHORT = "Value is too short";

    private CommonValidationConstants() {
    }
}
