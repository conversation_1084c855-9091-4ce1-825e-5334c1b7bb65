@echo off
echo ========================================
echo Testing New Error Handling System
echo ========================================

echo.
echo 1. Testing CommonExceptionHandling library...
cd "CommonExceptionHandling"
call mvn test -Dtest="*ErrorMapping*,*InterService*" -q
if %ERRORLEVEL% neq 0 (
    echo FAILED: CommonExceptionHandling tests failed
    goto :error
)
echo SUCCESS: CommonExceptionHandling tests passed

echo.
echo 2. Testing UserService error handling...
cd "..\UserService"
call mvn test -Dtest="*ExceptionHandling*" -q
if %ERRORLEVEL% neq 0 (
    echo FAILED: UserService tests failed
    goto :error
)
echo SUCCESS: UserService tests passed

echo.
echo 3. Testing ProjectService error handling...
cd "..\ProjectService"
call mvn test -Dtest="*ExceptionHandling*" -q
if %ERRORLEVEL% neq 0 (
    echo FAILED: ProjectService tests failed
    goto :error
)
echo SUCCESS: ProjectService tests passed

echo.
echo ========================================
echo All Error Handling Tests PASSED! ✓
echo ========================================
echo.
echo The new error handling system is working correctly:
echo - ✓ BaseExceptionHandler reduces code duplication
echo - ✓ ErrorMappingService standardizes error codes
echo - ✓ FeignErrorDecoder handles inter-service errors
echo - ✓ All services use consistent error format
echo.
goto :end

:error
echo.
echo ========================================
echo Tests FAILED! ✗
echo ========================================
echo Please check the error messages above and fix the issues.
echo.
exit /b 1

:end
cd ".."
pause
