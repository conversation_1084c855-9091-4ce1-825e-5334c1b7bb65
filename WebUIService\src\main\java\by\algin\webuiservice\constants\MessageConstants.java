package by.algin.webuiservice.constants;

public final class MessageConstants {
    
    public static final String PAGE_NOT_FOUND = "Page not found";
    public static final String TEMPLATE_NOT_FOUND = "Template not found";
    public static final String FORM_VALIDATION_FAILED = "Form validation failed";
    public static final String SESSION_EXPIRED = "Session expired";
    public static final String ACCESS_DENIED_UI = "Access denied";
    public static final String LOGIN_FAILED = "Login failed";
    public static final String LOGOUT_SUCCESSFUL = "Logout successful";
    public static final String LOGOUT_FAILED = "Logout failed";
    public static final String REGISTRATION_FAILED = "Registration failed";
    public static final String PROFILE_UPDATE_FAILED = "Profile update failed";
    public static final String PASSWORD_CHANGE_FAILED = "Password change failed";
    public static final String EMAIL_CONFIRMATION_FAILED = "Email confirmation failed";
    
    public static final String INTERNAL_SERVER_ERROR = "Internal server error";
    public static final String BAD_REQUEST = "Bad request";
    public static final String UNAUTHORIZED = "Unauthorized";
    public static final String FORBIDDEN = "Forbidden";
    public static final String NOT_FOUND = "Not found";
    public static final String METHOD_NOT_ALLOWED = "Method not allowed";
    public static final String REQUEST_TIMEOUT = "Request timeout";
    public static final String TOO_MANY_REQUESTS = "Too many requests";
    
    public static final String USER_SERVICE_UNAVAILABLE = "User service unavailable";
    public static final String EXTERNAL_SERVICE_ERROR = "External service error";
    public static final String MICROSERVICE_COMMUNICATION_FAILED = "Microservice communication failed";

    public static final String PROJECT_SERVICE_UNAVAILABLE = "Project service is currently unavailable";
    public static final String PROJECT_NOT_FOUND = "Project not found";
    public static final String INVALID_PROJECT_DATA = "Invalid project data. Please check your input.";
    public static final String PROJECT_NAME_EXISTS = "A project with this name already exists. Please choose a different name.";
    public static final String FAILED_TO_CREATE_PROJECT = "Failed to create project: ";
    public static final String FAILED_TO_UPDATE_PROJECT = "Failed to update project";
    public static final String FAILED_TO_DELETE_PROJECT = "Failed to delete project";
    public static final String FAILED_TO_ADD_MEMBER = "Failed to add member";
    public static final String FAILED_TO_REMOVE_MEMBER = "Failed to remove member";

    public static final String PROJECT_CREATED = "Project '%s' created successfully";
    public static final String PROJECT_UPDATED = "Project '%s' updated successfully";
    public static final String PROJECT_DELETED = "Project deleted successfully";
    public static final String MEMBER_ADDED = "Member added successfully";
    public static final String MEMBER_REMOVED = "Member removed successfully";

    public static final String INVALID_AUTH_RESPONSE = "Authentication response is invalid";
    public static final String REGISTRATION_EMAIL_ISSUE = "Registration successful, but there was an issue sending the confirmation email. Please check your email or contact the administrator.";
    public static final String ACCOUNT_CONFIRMED = "Account confirmed successfully! You can now log in.";
    public static final String AUTHENTICATION_CLEARED = "Authentication cookies cleared and security context cleared";
    public static final String ACCOUNT_CONFIRMATION_TOKEN_INVALID = "Account confirmation failed. The token may be invalid or expired.";
    public static final String ACCOUNT_CONFIRMATION_ERROR = "An error occurred during account confirmation";
    public static final String NETWORK_ERROR = "Network error occurred. Please check your connection and try again.";
    public static final String SERVICE_TEMPORARILY_UNAVAILABLE = "Service is temporarily unavailable. Please try again later.";
    public static final String SERVER_ERROR = "Unable to create project due to server error.";
    public static final String PERMISSION_DENIED_CREATE_PROJECTS = "You don't have permission to create projects.";
    public static final String USER_NOT_FOUND = "User not found";
    public static final String AUTHENTICATION_REQUIRED = "Authentication required";
    public static final String ACCOUNT_CONFIRMATION_FAILED = "Account confirmation failed";

    private MessageConstants() {}
}
