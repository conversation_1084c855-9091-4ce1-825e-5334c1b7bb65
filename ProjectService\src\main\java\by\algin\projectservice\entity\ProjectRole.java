package by.algin.projectservice.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.Set;

@Entity
@Table(name = "project_roles",
       uniqueConstraints = @UniqueConstraint(columnNames = {"role_name"}))
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectRole {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "role_name", nullable = false, unique = true, length = 50)
    private String roleName;

    @Column(name = "display_name", nullable = false, length = 100)
    private String displayName;

    @Column(name = "description", length = 500)
    private String description;

    @Column(name = "permission_level", nullable = false)
    private Integer permissionLevel;

    @Column(name = "is_active", nullable = false)
    @Builder.Default
    private Boolean isActive = true;

    @Column(name = "can_be_assigned", nullable = false)
    @Builder.Default
    private Boolean canBeAssigned = true;

    @Column(name = "max_per_project")
    private Integer maxPerProject;

    @Column(name = "is_system_role", nullable = false)
    @Builder.Default
    private Boolean isSystemRole = false;

    @Column(name = "color_code", length = 7)
    private String colorCode;

    @Column(name = "icon_name", length = 50)
    private String iconName;

    @Column(name = "created_by", length = 100)
    private String createdBy;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "updated_by", length = 100)
    private String updatedBy;

    @OneToMany(mappedBy = "role", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<ProjectRolePermission> permissions;

    public boolean hasHigherPermissionThan(ProjectRole otherRole) {
        return this.permissionLevel > otherRole.getPermissionLevel();
    }

    public boolean hasPermissionLevelAtLeast(Integer requiredLevel) {
        return this.permissionLevel >= requiredLevel;
    }

    public boolean canBeAssignedToUsers() {
        return isActive && canBeAssigned;
    }

    public boolean isMaxLimitReached(int currentCount) {
        return maxPerProject != null && currentCount >= maxPerProject;
    }
}
