package by.algin.projectservice.service;

import by.algin.projectservice.config.AppProperties;
import by.algin.projectservice.entity.ProjectRole;
import by.algin.projectservice.entity.ProjectRolePermission;
import by.algin.projectservice.repository.ProjectRoleRepository;
import by.algin.projectservice.repository.ProjectRolePermissionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ProjectRoleService {

    private final ProjectRoleRepository roleRepository;
    private final ProjectRolePermissionRepository permissionRepository;
    private final AppProperties appProperties;
    private final RoleConfigurationService roleConfigurationService;



    public List<ProjectRole> getAllActiveRoles() {
        return roleRepository.findByIsActiveTrueOrderByPermissionLevelDesc();
    }

    public List<ProjectRole> getAssignableRoles() {
        return roleRepository.findByIsActiveTrueAndCanBeAssignedTrueOrderByPermissionLevelDesc();
    }

    public Optional<ProjectRole> findRoleByName(String roleName) {
        return roleRepository.findByRoleNameIgnoreCase(roleName);
    }

    public ProjectRole getRoleByNameOrThrow(String roleName) {
        return findRoleByName(roleName)
            .orElseThrow(() -> new IllegalArgumentException("Role not found: " + roleName));
    }

    public boolean hasPermission(String roleName, String permissionName) {
        if (roleConfigurationService.hasPermission(roleName, permissionName)) {
            return true;
        }

        return permissionRepository.hasPermissionByRoleName(roleName, permissionName);
    }

    public List<ProjectRolePermission> getRolePermissions(String roleName) {
        return permissionRepository.findGrantedPermissionsByRoleName(roleName);
    }

    public Set<String> getRolePermissionNames(String roleName) {
        Set<String> configPermissions = roleConfigurationService.getRolePermissions(roleName);
        if (!configPermissions.isEmpty()) {
            return configPermissions;
        }

        return permissionRepository.findGrantedPermissionsByRoleName(roleName)
            .stream()
            .map(ProjectRolePermission::getPermissionName)
            .collect(Collectors.toSet());
    }

    public boolean canAssignRole(String roleName) {
        return findRoleByName(roleName)
            .map(ProjectRole::canBeAssignedToUsers)
            .orElse(false);
    }

    public boolean hasHigherPermissionLevel(String roleName1, String roleName2) {
        Optional<ProjectRole> role1 = findRoleByName(roleName1);
        Optional<ProjectRole> role2 = findRoleByName(roleName2);

        if (role1.isEmpty() || role2.isEmpty()) {
            return false;
        }

        return role1.get().hasHigherPermissionThan(role2.get());
    }

    public List<ProjectRole> getRolesWithMinLevel(Integer minLevel) {
        return roleRepository.findByIsActiveTrueAndPermissionLevelGreaterThanEqualOrderByPermissionLevelDesc(minLevel);
    }

    public List<ProjectRole> getAssignableRolesWithMinLevel(Integer minLevel) {
        return roleRepository.findAssignableRolesWithMinLevel(minLevel);
    }

    public boolean isMaxRoleLimitReached(String roleName, int currentCount) {
        return findRoleByName(roleName)
            .map(role -> role.isMaxLimitReached(currentCount))
            .orElse(false);
    }

    public List<String> getAllAvailablePermissions() {
        return permissionRepository.findAllDistinctPermissionNames();
    }

    public ProjectRole getOwnerRole() {
        return getRoleByNameOrThrow(appProperties.getRole().getNames().getOwner());
    }

    public ProjectRole getManagerRole() {
        return getRoleByNameOrThrow(appProperties.getRole().getNames().getManager());
    }

    public Set<String> getAvailableRolesFromConfig() {
        return roleConfigurationService.getAvailableRoles();
    }

    public ProjectRole getDeveloperRole() {
        return getRoleByNameOrThrow(appProperties.getRole().getNames().getDeveloper());
    }

    public ProjectRole getViewerRole() {
        return getRoleByNameOrThrow(appProperties.getRole().getNames().getViewer());
    }

    public boolean isOwnerRole(String roleName) {
        return appProperties.getRole().getNames().getOwner().equals(roleName);
    }

    public boolean isManagerRole(String roleName) {
        return appProperties.getRole().getNames().getManager().equals(roleName);
    }

    public boolean isManagementRole(String roleName) {
        return isOwnerRole(roleName) || isManagerRole(roleName);
    }
}
