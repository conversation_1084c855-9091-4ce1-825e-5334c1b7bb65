package by.algin.api;

import by.algin.dto.project.ProjectRoleResponse;
import by.algin.dto.response.ApiResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * Common API interface for Project Role operations
 * This interface should be implemented by ProjectService and used by Feign clients
 *
 * Benefits:
 * - Single source of truth for role API
 * - Consistent endpoints across services
 * - Type safety for Feign clients
 * - Easy to extend and maintain
 */
@RequestMapping("/api/projects/roles")
public interface ProjectRoleApi {

    /**
     * Get all available project roles
     * @return List of all project roles with their configurations
     */
    @GetMapping
    ApiResponse<List<ProjectRoleResponse>> getAvailableRoles();

    /**
     * Get only assignable project roles (active and can be assigned)
     * Used by WebUI forms for role selection
     * @return List of assignable project roles
     */
    @GetMapping("/assignable")
    ApiResponse<List<ProjectRoleResponse>> getAssignableRoles();
}
