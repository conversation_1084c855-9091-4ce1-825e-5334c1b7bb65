package by.algin.projectservice.repository;

import by.algin.projectservice.entity.ProjectRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProjectRoleRepository extends JpaRepository<ProjectRole, Long> {

    Optional<ProjectRole> findByRoleNameIgnoreCase(String roleName);

    List<ProjectRole> findByIsActiveTrueOrderByPermissionLevelDesc();

    List<ProjectRole> findByIsActiveTrueAndCanBeAssignedTrueOrderByPermissionLevelDesc();

    List<ProjectRole> findByIsActiveTrueAndPermissionLevelGreaterThanEqualOrderByPermissionLevelDesc(Integer minLevel);

    @Query("SELECT r FROM ProjectRole r WHERE r.isActive = true AND r.canBeAssigned = true " +
           "AND r.permissionLevel >= :minLevel ORDER BY r.permissionLevel DESC")
    List<ProjectRole> findAssignableRolesWithMinLevel(@Param("minLevel") Integer minLevel);

    Optional<ProjectRole> findTopByIsActiveTrueOrderByPermissionLevelDesc();

    Optional<ProjectRole> findTopByIsActiveTrueOrderByPermissionLevelAsc();

    boolean existsByRoleNameIgnoreCase(String roleName);

    List<ProjectRole> findByCreatedByOrderByCreatedAtDesc(String createdBy);

    List<ProjectRole> findByIsSystemRoleTrueOrderByPermissionLevelDesc();

    List<ProjectRole> findByIsSystemRoleFalseOrderByPermissionLevelDesc();

    List<ProjectRole> findByMaxPerProjectIsNotNullOrderByMaxPerProjectAsc();

    long countByIsActiveTrue();

    @Query("SELECT r FROM ProjectRole r WHERE r.isActive = true " +
           "AND r.permissionLevel BETWEEN :minLevel AND :maxLevel " +
           "ORDER BY r.permissionLevel DESC")
    List<ProjectRole> findRolesByPermissionLevelRange(@Param("minLevel") Integer minLevel,
                                                     @Param("maxLevel") Integer maxLevel);
}
