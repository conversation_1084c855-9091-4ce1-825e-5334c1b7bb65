.members-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(var(--grid-min-width), 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.members-grid.wide {
    grid-template-columns: repeat(auto-fill, minmax(var(--grid-min-width-wide), 1fr));
    gap: var(--spacing-lg);
}

.member-card {
    background-color: var(--bg-card);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color-light);
    position: relative;
}

.member-card.detailed {
    padding: var(--spacing-lg);
}

.member-name {
    font-weight: bold;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.member-name.large {
    font-size: var(--font-size-lg);
}

.member-role {
    color: var(--text-muted);
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-xs);
}

.member-joined {
    color: var(--text-muted);
    font-size: var(--font-size-md);
    margin-bottom: var(--spacing-md);
}

.member-actions {
    margin-top: var(--spacing-md);
}

.role-owner { color: var(--role-owner-color); font-weight: bold; }
.role-manager { color: var(--role-manager-color); font-weight: bold; }
.role-developer { color: var(--role-developer-color); }
.role-viewer { color: var(--role-viewer-color); }
