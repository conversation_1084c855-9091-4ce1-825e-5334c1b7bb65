package by.algin.userservice.exception;

import by.algin.common.BaseExceptionHandler;
import by.algin.common.exception.CommonErrorCodes;
import by.algin.common.exception.UserNotFoundException;
import by.algin.common.exception.EmailAlreadyExistsException;
import by.algin.common.exception.UsernameAlreadyExistsException;
import by.algin.dto.response.ApiResponse;
import by.algin.userservice.constants.MessageConstants;
import by.algin.userservice.constants.UserServiceReasonKeys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

/**
 * UserService specific exception handler
 * 
 * Extends BaseExceptionHandler to inherit common fallback handlers and helper methods.
 * Adds UserService-specific exception handlers for domain-specific errors.
 * 
 * Uses CommonErrorCodes from CommonExceptionHandling library for standardized error codes.
 */
@Slf4j
@ControllerAdvice
public class UserServiceGlobalExceptionHandler extends BaseExceptionHandler {

    @Override
    protected String getServiceName() {
        return MessageConstants.SERVICE_NAME;
    }

    // ========== AUTHENTICATION & SECURITY HANDLERS ==========

    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity<ApiResponse<Object>> handleBadCredentials(BadCredentialsException ex) {
        log.warn("Bad credentials: {}", ex.getMessage());
        return createError(
            CommonErrorCodes.INVALID_CREDENTIALS.getCode(),
            ex.getMessage(),
            CommonErrorCodes.INVALID_CREDENTIALS.getHttpStatus().value(),
            createReasonDetails(UserServiceReasonKeys.REASON_BAD_CREDENTIALS, ex)
        );
    }

    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<ApiResponse<Object>> handleAuthentication(AuthenticationException ex) {
        log.warn("Authentication failed: {}", ex.getMessage());
        return createError(
            CommonErrorCodes.AUTHENTICATION_FAILED.getCode(),
            ex.getMessage(),
            CommonErrorCodes.AUTHENTICATION_FAILED.getHttpStatus().value(),
            createReasonDetails(UserServiceReasonKeys.REASON_AUTHENTICATION_FAILED, ex)
        );
    }

    // ========== USER DOMAIN-SPECIFIC HANDLERS ==========

    @ExceptionHandler(UserNotFoundException.class)
    public ResponseEntity<ApiResponse<Object>> handleUserNotFound(UserNotFoundException ex) {
        log.warn("User not found: {}", ex.getMessage());
        return createError(
            CommonErrorCodes.USER_NOT_FOUND.getCode(),
            ex.getMessage(),
            CommonErrorCodes.USER_NOT_FOUND.getHttpStatus().value(),
            createReasonDetails(UserServiceReasonKeys.REASON_USER_NOT_FOUND, ex)
        );
    }

    @ExceptionHandler(EmailAlreadyExistsException.class)
    public ResponseEntity<ApiResponse<Object>> handleEmailAlreadyExists(EmailAlreadyExistsException ex) {
        log.warn("Email already exists: {}", ex.getMessage());
        return createError(
            CommonErrorCodes.EMAIL_ALREADY_EXISTS.getCode(),
            ex.getMessage(),
            CommonErrorCodes.EMAIL_ALREADY_EXISTS.getHttpStatus().value(),
            createReasonDetails(UserServiceReasonKeys.REASON_ALREADY_EXISTS, ex)
        );
    }

    @ExceptionHandler(UsernameAlreadyExistsException.class)
    public ResponseEntity<ApiResponse<Object>> handleUsernameAlreadyExists(UsernameAlreadyExistsException ex) {
        log.warn("Username already exists: {}", ex.getMessage());
        return createError(
            CommonErrorCodes.USERNAME_ALREADY_EXISTS.getCode(),
            ex.getMessage(),
            CommonErrorCodes.USERNAME_ALREADY_EXISTS.getHttpStatus().value(),
            createReasonDetails(UserServiceReasonKeys.REASON_ALREADY_EXISTS, ex)
        );
    }

    // ========== VALIDATION HANDLERS ==========

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiResponse<Object>> handleIllegalArgument(IllegalArgumentException ex) {
        log.warn("Invalid argument: {}", ex.getMessage());
        return createError(
            CommonErrorCodes.INVALID_INPUT.getCode(),
            ex.getMessage(),
            CommonErrorCodes.INVALID_INPUT.getHttpStatus().value(),
            createReasonDetails(UserServiceReasonKeys.REASON_VALIDATION_FAILED, ex)
        );
    }
}
