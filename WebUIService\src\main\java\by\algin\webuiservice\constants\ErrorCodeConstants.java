package by.algin.webuiservice.constants;

public final class ErrorCodeConstants {
    
    public static final String INVALID_CREDENTIALS = "INVALID_CREDENTIALS";
    public static final String ACCOUNT_DISABLED = "ACCOUNT_DISABLED";
    public static final String ACCOUNT_LOCKED = "ACCOUNT_LOCKED";
    public static final String SESSION_ERROR = "SESSION_ERROR";
    
    public static final String EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR";
    public static final String SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE";
    public static final String MICROSERVICE_TIMEOUT = "MICROSERVICE_TIMEOUT";
    
    public static final String TEMPLATE_ERROR = "TEMPLATE_ERROR";
    public static final String PAGE_NOT_FOUND = "PAGE_NOT_FOUND";
    
    public static final String VALIDATION_ERROR = "VALIDATION_ERROR";
    public static final String FORM_VALIDATION_FAILED = "FORM_VALIDATION_FAILED";
    public static final String EMAIL_ALREADY_EXISTS = "EMAIL_ALREADY_EXISTS";
    public static final String USERNAME_ALREADY_EXISTS = "USERNAME_ALREADY_EXISTS";
    public static final String PASSWORDS_DONT_MATCH = "PASSWORDS_DONT_MATCH";
    public static final String INVALID_EMAIL = "INVALID_EMAIL";
    
    public static final String INVALID_TOKEN = "INVALID_TOKEN";
    public static final String EXPIRED_TOKEN = "EXPIRED_TOKEN";
    
    public static final String INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR";
    public static final String USER_NOT_FOUND = "USER_NOT_FOUND";
    public static final String RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED";
    
    public static final String REGISTRATION_SUCCESS = "REGISTRATION_SUCCESS";
    
}
