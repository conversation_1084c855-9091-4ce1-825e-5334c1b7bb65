
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*
replay_pid*

target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

.idea/
*.iws
*.iml
*.ipr
out/
.idea_modules/

.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders
.project
.classpath
.factorypath
.buildpath
.target

.vscode/
*.code-workspace

*.log
logs/
log/

*.tmp
*.temp
*~
.DS_Store
Thumbs.db

.env
.env.local
.env.development.local
.env.test.local
.env.production.local

spring-shell.log
.spring-boot-devtools.properties

*.db
*.sqlite
*.sqlite3

.dockerignore
docker-compose.override.yml

.gradle/
build/
gradle-app.setting
!gradle-wrapper.jar

node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

*.zip
*.tar.gz
*.rar
*.7z

temp_repos/
*.backup

tests/

*/src/test/
**/src/test/
test/
**/test/

### Documentation and Configuration ###
*.md
application.yml
application-*.yml
