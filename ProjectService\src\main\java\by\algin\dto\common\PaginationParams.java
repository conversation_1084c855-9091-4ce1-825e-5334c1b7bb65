package by.algin.dto.common;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

@Data
@NoArgsConstructor
public class PaginationParams {

    private Integer page = 0;
    private Integer size = 20;
    private String sortBy = "createdAt";
    private String sortDir = "desc";

    public PaginationParams(Integer page, Integer size, String sortBy, String sortDir) {
        this.page = page != null ? page : 0;
        this.size = size != null ? size : 20;
        this.sortBy = sortBy != null ? sortBy : "createdAt";
        this.sortDir = sortDir != null ? sortDir : "desc";
    }

    public Pageable toPageable() {
        Sort.Direction direction = "desc".equalsIgnoreCase(sortDir)
            ? Sort.Direction.DESC
            : Sort.Direction.ASC;

        Sort sort = Sort.by(direction, sortBy);
        return PageRequest.of(page != null ? page : 0, size != null ? size : 20, sort);
    }
}
