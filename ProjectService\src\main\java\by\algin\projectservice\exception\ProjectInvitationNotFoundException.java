package by.algin.projectservice.exception;

import lombok.Getter;

@Getter
public class ProjectInvitationNotFoundException extends RuntimeException {
    private final Long invitationId;
    private final String token;

    public ProjectInvitationNotFoundException(Long invitationId) {
        super("Project invitation not found with ID: " + invitationId);
        this.invitationId = invitationId;
        this.token = null;
    }

    public ProjectInvitationNotFoundException(String token) {
        super("Project invitation not found with token: " + token);
        this.invitationId = null;
        this.token = token;
    }
}
