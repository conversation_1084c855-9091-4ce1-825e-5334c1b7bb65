package by.algin.projectservice.constants;

public final class ProjectMessageConstants {

    public static final String SERVICE_NAME = "PROJECT-SERVICE";

    public static final String DETAIL_KEY_REASON = "reason";
    public static final String DETAIL_KEY_EXCEPTION_TYPE = "exceptionType";
    public static final String DETAIL_KEY_TRACE_ID = "traceId";
    public static final String DETAIL_KEY_SERVICE_NAME = "serviceName";
    public static final String DETAIL_KEY_CATEGORY = "category";
    public static final String DETAIL_KEY_ORIGINAL_MESSAGE = "originalMessage";
    public static final String DETAIL_KEY_PROJECT_ID = "projectId";
    public static final String DETAIL_KEY_USER_ID = "userId";
    public static final String DETAIL_KEY_PROJECT_NAME = "projectName";
    public static final String DETAIL_KEY_INVITATION_ID = "invitationId";
    public static final String DETAIL_KEY_TOKEN = "token";
    public static final String DETAIL_KEY_FROM_STATUS = "fromStatus";
    public static final String DETAIL_KEY_TO_STATUS = "toStatus";
    public static final String DETAIL_KEY_USER_ROLE = "userRole";
    public static final String DETAIL_KEY_TARGET_SERVICE = "targetService";
    public static final String DETAIL_KEY_ORIGINAL_ERROR_CODE = "originalErrorCode";
    public static final String DETAIL_KEY_HTTP_STATUS = "httpStatus";
    public static final String DETAIL_KEY_FIELD_ERRORS = "fieldErrors";
    public static final String DETAIL_KEY_VALIDATION_ERROR = "validationError";
    public static final String DETAIL_KEY_CONFIGURATION_ISSUE = "configurationIssue";

    public static final String PROJECT_DELETED_SUCCESSFULLY = "Project deleted successfully";
    public static final String MEMBER_REMOVED_SUCCESSFULLY = "Member removed successfully";
    public static final String INVITATION_ACCEPTED_SUCCESSFULLY = "Invitation accepted successfully";
    public static final String INVITATION_CANCELED_SUCCESSFULLY = "Invitation canceled successfully";
    public static final String CONFIGURATION_VALIDATION_COMPLETED = "Configuration validation completed successfully. Check logs for details.";

    public static final String CONFIGURATION_VALIDATION_FAILED = "Configuration validation failed";
    public static final String CONFIGURATION_VALIDATION_FAILED_WITH_MESSAGE = "Configuration validation failed: ";
    public static final String TOKEN_VALIDATION_FAILED_UNAUTHORIZED = "Token validation failed - unauthorized";
    public static final String USER_SERVICE_TOKEN_VALIDATION_FAILED = "UserService token validation failed, falling back to local validation: {}";
    public static final String JWT_TOKEN_VALIDATION_FAILED_FOR_USER = "JWT token validation failed for user: {} (local: {}, remote: {})";
    public static final String JWT_TOKEN_VALIDATION_FAILED = "JWT token validation failed: {}";

    public static final String EXTRACTED_USERNAME_AND_USERID = "Extracted username: {} and userId: {} from JWT token";
    public static final String UNABLE_TO_EXTRACT_FROM_JWT = "Unable to extract username/userId from JWT token: {}";
    public static final String JWT_TOKEN_NOT_BEARER = "JWT Token does not begin with Bearer String";
    public static final String AUTHENTICATION_SET_FOR_USER = "Authentication set for user: {} (userId: {})";
    public static final String INVALID_USER_ID_FORMAT_IN_JWT = "Invalid user ID format in JWT token: {}";
    public static final String INVALID_JWT_TOKEN = "Invalid JWT token: {}";

    public static final String INVALID_STATUS_IN_CONFIGURATION = "Invalid status in configuration: {}";
    public static final String INVALID_STATUS_IN_TRANSITIONS_CONFIGURATION = "Invalid status in transitions configuration: {}";
    public static final String INVALID_TRANSITION_CONFIGURATION_NULL_TARGET = "Invalid transition configuration for status {}: contains null target";

    public static final String INVALID_INVITATION_TOKEN = "Invalid invitation token";
    public static final String INVALID_EMAIL_FORMAT = "Invalid email format";

    public static final String PROJECT_NAME_CANNOT_BE_EMPTY = "Project name cannot be empty";
    public static final String PROJECT_NAME_MIN_LENGTH = "Project name must be at least ";
    public static final String PROJECT_NAME_MAX_LENGTH = "Project name must be at most ";
    public static final String PROJECT_DESCRIPTION_MAX_LENGTH = "Project description cannot exceed ";
    public static final String EMAIL_CANNOT_BE_EMPTY = "Email cannot be empty";
    public static final String PAGE_NUMBER_CANNOT_BE_NEGATIVE = "Page number cannot be negative";
    public static final String PAGE_SIZE_MUST_BE_POSITIVE = "Page size must be positive";
    public static final String PAGE_SIZE_CANNOT_EXCEED = "Page size cannot exceed ";
    public static final String FIELD_CANNOT_BE_NULL = " cannot be null";
    public static final String FIELD_MUST_BE_POSITIVE = " must be positive";
    public static final String CHARACTERS_LONG = " characters long";
    public static final String CHARACTERS = " characters";

    public static final String CANNOT_CHANGE_ROLE_OF_PROJECT_OWNER = "Cannot change role of project owner";
    public static final String CANNOT_PROMOTE_MEMBER_TO_OWNER_ROLE = "Cannot promote member to owner role";
    public static final String ONLY_PROJECT_OWNER_CAN_DELETE_PROJECT = "Only project owner can delete the project";
    public static final String CANNOT_REMOVE_PROJECT_OWNER_FROM_PROJECT = "Cannot remove project owner from project";
    public static final String CANNOT_MODIFY_ARCHIVED_PROJECT = "Cannot modify archived project";
    public static final String CANNOT_MODIFY_COMPLETED_PROJECT = "Cannot modify completed project";
    public static final String MEMBER_ROLE_CANNOT_BE_NULL = "Member role cannot be null";
    public static final String CANNOT_ADD_MEMBERS_TO_ARCHIVED_PROJECT = "Cannot add members to archived project";
    public static final String CANNOT_ADD_MEMBERS_TO_COMPLETED_PROJECT = "Cannot add members to completed project";
    public static final String CANNOT_ADD_MEMBERS_TO_INACTIVE_PROJECT = "Cannot add members to inactive project";

    public static final String LOG_CREATING_INVITATION_TO_PROJECT = "Creating invitation to project {}";
    public static final String LOG_CREATING_INVITATION_BY_USER = "Creating invitation to project {} by user: {}";
    public static final String LOG_SUCCESSFULLY_CREATED_INVITATION = "Successfully created invitation with ID: {}";
    public static final String LOG_GETTING_INVITATIONS_BY_USER = "Getting invitations for project {} by user: {}";
    public static final String LOG_GETTING_INVITATIONS_WITH_PAGINATION = "Getting invitations for project: {} (page={}, size={}, sortBy={}, sortDir={})";
    public static final String LOG_GETTING_INVITATION_BY_TOKEN = "Getting invitation by token: {}";
    public static final String LOG_ACCEPTING_INVITATION = "Accepting invitation with token: {}";
    public static final String LOG_USER_ACCEPTING_INVITATION = "User {} accepting invitation with token: {}";
    public static final String LOG_SUCCESSFULLY_ACCEPTED_INVITATION = "Successfully accepted invitation with token: {}";
    public static final String LOG_CANCELING_INVITATION = "Canceling invitation {} for project: {}";
    public static final String LOG_USER_CANCELING_INVITATION = "User {} canceling invitation {} for project: {}";
    public static final String LOG_SUCCESSFULLY_CANCELED_INVITATION = "Successfully canceled invitation {} for project: {}";

    public static final String LOG_CREATING_PROJECT = "Creating project: {}";
    public static final String LOG_CREATING_PROJECT_FOR_USER = "Creating project for user: {}";
    public static final String LOG_SUCCESSFULLY_CREATED_PROJECT = "Successfully created project with ID: {}";
    public static final String LOG_GETTING_PROJECT = "Getting project: {}";
    public static final String LOG_GETTING_PROJECT_FOR_USER = "Getting project {} for user: {}";
    public static final String LOG_UPDATING_PROJECT = "Updating project: {}";
    public static final String LOG_UPDATING_PROJECT_FOR_USER = "Updating project {} for user: {}";
    public static final String LOG_DELETING_PROJECT = "Deleting project: {}";
    public static final String LOG_DELETING_PROJECT_FOR_USER = "Deleting project {} for user: {}";
    public static final String LOG_GETTING_PROJECTS_FOR_CURRENT_USER = "Getting projects for current user: {} (page={}, size={}, sortBy={}, sortDir={})";
    public static final String LOG_GETTING_PROJECTS_FOR_USER = "Getting projects for user: {} (requested by: {}, page={}, size={}, sortBy={}, sortDir={})";
    public static final String LOG_ADDING_MEMBER_TO_PROJECT = "Adding member to project: {}";
    public static final String LOG_ADDING_MEMBER_BY_USER = "Adding member to project {} by user: {}";
    public static final String LOG_SUCCESSFULLY_ADDED_MEMBER = "Successfully added member to project {}";
    public static final String LOG_UPDATING_MEMBER_ROLE = "Updating member role in project: {} for user: {}";
    public static final String LOG_UPDATING_MEMBER_ROLE_BY_USER = "Updating member role in project {} for user {} by user: {}";
    public static final String LOG_REMOVING_MEMBER_FROM_PROJECT = "Removing member from project: {} user: {}";
    public static final String LOG_REMOVING_MEMBER_BY_USER = "Removing member from project {} user {} by user: {}";
    public static final String LOG_SUCCESSFULLY_REMOVED_MEMBER = "Successfully removed member from project {}";
    public static final String LOG_GETTING_MEMBERS_FOR_USER = "Getting members of project {} for user: {}";
    public static final String LOG_GETTING_MEMBERS_WITH_PAGINATION = "Getting members of project: {} (page={}, size={}, sortBy={}, sortDir={})";
    public static final String LOG_TEST_ENDPOINT_CALLED = "Test endpoint called";

    public static final String TEST_SERVICE_WORKING = "ProjectService is working!";

    public static final String LOG_GETTING_ALL_STATUS_TRANSITIONS = "Getting all status transitions";
    public static final String LOG_GETTING_TRANSITIONS_FOR_STATUS = "Getting transitions for status: {}";
    public static final String LOG_VALIDATING_TRANSITION = "Validating transition from {} to {} for role {}";
    public static final String LOG_GETTING_TRANSITIONS_DESCRIPTION = "Getting transitions description";
    public static final String LOG_VALIDATING_STATUS_TRANSITIONS_CONFIG = "Validating status transitions configuration";

    public static final String HEALTH_STATUS_UP = "UP";
    public static final String HEALTH_SERVICE_NAME = "ProjectService";
    public static final String HEALTH_VERSION = "1.0.0";
    public static final String HEALTH_STATUS_KEY = "status";
    public static final String HEALTH_SERVICE_KEY = "service";
    public static final String HEALTH_TIMESTAMP_KEY = "timestamp";
    public static final String HEALTH_VERSION_KEY = "version";

    public static final String LOG_PROJECT_CREATED = "Project created - ID: {}, Owner: {}";
    public static final String LOG_PROJECT_DELETED = "Project deleted - ID: {}, Owner: {}";
    public static final String LOG_MEMBER_ADDED = "Member added - Project: {}, User: {}, Role: {}";
    public static final String LOG_MEMBER_REMOVED = "Member removed - Project: {}, User: {}, Role: {}";

    public static final String LOG_CREATING_INVITATION_SERVICE = "Creating invitation to project {} by user {} (intended for: {})";
    public static final String LOG_INVITATION_CREATED_WITH_ID = "Invitation created with ID {} for project {}";
    public static final String LOG_USER_ACCEPTING_INVITATION_SERVICE = "User {} accepting invitation with token {}";
    public static final String LOG_INVITATION_ACCEPTED_BY_USER = "Invitation {} accepted by user {} ({})";
    public static final String LOG_USER_CANCELING_INVITATION_SERVICE = "User {} canceling invitation {}";
    public static final String LOG_INVITATION_CANCELLED_BY_USER = "Invitation {} cancelled by user {}";
    public static final String LOG_CLEANING_UP_EXPIRED_INVITATIONS = "Cleaning up expired invitations";
    public static final String LOG_MARKED_INVITATIONS_AS_EXPIRED = "Marked {} invitations as expired";

    private ProjectMessageConstants() {
    }
}
