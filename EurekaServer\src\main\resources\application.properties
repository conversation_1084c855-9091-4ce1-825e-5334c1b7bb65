# Eureka Server Configuration
spring.application.name=EUREKA-SERVER
server.port=8761

# Eureka Server Settings
eureka.client.registerWithEureka=false
eureka.client.fetchRegistry=false
eureka.client.serviceUrl.defaultZone=http://localhost:8761/eureka/

# Server Instance Configuration
eureka.instance.hostname=localhost

# Disable self-preservation mode for development
eureka.server.enable-self-preservation=false
eureka.server.eviction-interval-timer-in-ms=5000