package by.algin.projectservice.config;

import lombok.Data;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

import java.util.Map;
import java.util.List;
import java.util.Set;

@Data
public class RoleConfiguration {
    
    @Valid
    @NotNull
    private Map<String, RoleDefinition> definitions = Map.of();
    
    @Valid
    @NotNull
    private Map<String, PermissionDefinition> permissions = Map.of();
    
    @Valid
    @NotNull
    private Map<String, List<String>> permissionGroups = Map.of();
    
    @Data
    public static class RoleDefinition {
        @NotNull
        @NotEmpty
        private String displayName;
        
        private String description;
        
        @NotNull
        @Min(1)
        @Max(100)
        private Integer permissionLevel;
        
        private String color;
        private String icon;
        private Integer maxPerProject;
        private Boolean isActive = true;
        private Boolean canBeAssigned = true;
        private Boolean isSystemRole = false;
        
        @NotNull
        private Set<String> permissions = Set.of();
        
        private Set<String> permissionGroups = Set.of();
    }
    
    @Data
    public static class PermissionDefinition {
        @NotNull
        @NotEmpty
        private String description;
        
        private String category;
        private Boolean isActive = true;
        private String displayName;
    }
}
