package by.algin.webuiservice.client;

import by.algin.constants.CommonPathConstants;
import by.algin.constants.CommonServiceConstants;
import by.algin.dto.request.LoginRequest;
import by.algin.dto.request.RegisterRequest;
import by.algin.dto.request.TokenValidationRequest;
import by.algin.dto.response.ApiResponse;
import by.algin.dto.response.AuthResponse;
import by.algin.dto.response.TokenValidationResponse;
import by.algin.dto.response.UserResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(
    name = CommonServiceConstants.USER_SERVICE, 
    contextId = "${app.feign-clients.auth-service.context-id}", 
    path = "${app.feign-clients.auth-service.path}"
)
public interface AuthServiceClient {

    @PostMapping(CommonPathConstants.REGISTER_ENDPOINT)
    ApiResponse<UserResponse> registerUser(@RequestBody RegisterRequest user);

    @GetMapping(CommonPathConstants.CONFIRM_ENDPOINT)
    ApiResponse<String> confirmAccount(@RequestParam(CommonPathConstants.PARAM_TOKEN) String token);

    @PostMapping(CommonPathConstants.RESEND_CONFIRMATION_ENDPOINT)
    ApiResponse<String> resendConfirmation(@RequestParam(CommonPathConstants.PARAM_EMAIL) String email);

    @PostMapping(CommonPathConstants.LOGIN_ENDPOINT)
    ApiResponse<AuthResponse> loginUser(@RequestBody LoginRequest loginRequest);

    @PostMapping(CommonPathConstants.API_AUTH_VALIDATE_TOKEN)
    ApiResponse<TokenValidationResponse> validateToken(@RequestBody TokenValidationRequest request);

    @GetMapping(CommonPathConstants.EMAIL_BY_TOKEN_ENDPOINT)
    ApiResponse<String> getEmailByToken(@RequestParam(CommonPathConstants.PARAM_TOKEN) String token);
}
