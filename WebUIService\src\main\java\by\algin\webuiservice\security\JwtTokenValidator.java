package by.algin.webuiservice.security;

import by.algin.dto.request.TokenValidationRequest;
import by.algin.dto.response.ApiResponse;
import by.algin.dto.response.TokenValidationResponse;
import by.algin.webuiservice.client.AuthServiceClient;
import by.algin.common.exception.TokenExpiredException;
import lombok.Builder;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

@Slf4j
@Component
@RequiredArgsConstructor
public class JwtTokenValidator {

    private final AuthServiceClient authServiceClient;

    public JwtValidationResult validateToken(String token) {
        try {
            TokenValidationRequest request = new TokenValidationRequest();
            request.setToken(token);

            ApiResponse<TokenValidationResponse> response = authServiceClient.validateToken(request);
            if (response.isSuccess() && response.getData() != null) {
                TokenValidationResponse validationData = response.getData();
                log.debug("Validation result: username={}, roles={}", validationData.getUsername(), validationData.getRoles());
                return JwtValidationResult.builder()
                        .valid(validationData.isValid())
                        .username(validationData.getUsername())
                        .userId(validationData.getUserId())
                        .roles(validationData.getRoles() != null ? validationData.getRoles() : new HashSet<>())
                        .build();
            } else {
                log.warn("Token validation failed: {}", response.getMessage());
                if (response.getMessage() != null && response.getMessage().toLowerCase().contains("expired")) {
                    throw new TokenExpiredException("Token has expired: " + response.getMessage());
                }
                return JwtValidationResult.invalid("Token validation failed: " + response.getMessage());
            }
        } catch (Exception e) {
            log.error("Error validating token", e);
            return JwtValidationResult.invalid("Token validation error");
        }
    }

    @Getter
    @Builder
    public static class JwtValidationResult {
        private final boolean valid;
        private final String username;
        private final Long userId;
        private final Set<String> roles;
        private final String errorMessage;

        public static JwtValidationResult invalid(String errorMessage) {
            return JwtValidationResult.builder()
                    .valid(false)
                    .roles(new HashSet<>())
                    .errorMessage(errorMessage)
                    .build();
        }

        public static JwtValidationResult valid(String username, Long userId, Set<String> roles) {
            return JwtValidationResult.builder()
                    .valid(true)
                    .username(username)
                    .userId(userId)
                    .roles(roles)
                    .build();
        }
    }
}