package by.algin.projectservice.service;

import by.algin.common.exception.UserNotFoundException;
import by.algin.dto.project.InvitationStatus;
import by.algin.dto.project.CreateInvitationRequest;
import by.algin.projectservice.constants.ProjectMessageConstants;
import by.algin.dto.project.InvitationResponse;
import by.algin.dto.response.UserResponse;
import by.algin.projectservice.util.UserServiceClient;
import by.algin.projectservice.entity.Project;
import by.algin.projectservice.entity.ProjectInvitation;
import by.algin.projectservice.entity.ProjectMember;
import by.algin.projectservice.exception.ProjectInvitationNotFoundException;
import by.algin.projectservice.exception.ProjectNotFoundException;
import by.algin.projectservice.exception.DuplicateProjectMemberException;
import by.algin.projectservice.repository.ProjectRepository;
import by.algin.projectservice.repository.ProjectInvitationRepository;
import by.algin.projectservice.repository.ProjectMemberRepository;
import by.algin.projectservice.config.AppProperties;
import by.algin.projectservice.service.ProjectRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class ProjectInvitationService {

    private final ProjectInvitationRepository invitationRepository;
    private final ProjectRepository projectRepository;
    private final ProjectMemberRepository memberRepository;
    private final UserServiceClient userServiceClient;
    private final ProjectPermissionService permissionService;
    private final ProjectBusinessRulesService businessRulesService;
    private final AppProperties appProperties;
    private final ProjectRoleService roleService;

    public InvitationResponse createInvitation(Long projectId, CreateInvitationRequest request, Long inviterId) {
        log.info(ProjectMessageConstants.LOG_CREATING_INVITATION_SERVICE,
                projectId, inviterId, request.getEmail() != null ? request.getEmail() : "anyone");

        permissionService.requireMemberManagementAccess(projectId, inviterId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new ProjectNotFoundException(projectId));


        by.algin.projectservice.entity.ProjectRole entityRole = roleService.getRoleByNameOrThrow(request.getRole().name());

        businessRulesService.validateMemberAddition(project, null, entityRole);

        ProjectInvitation invitation = ProjectInvitation.builder()
                .project(project)
                .invitedEmail(request.getEmail())
                .role(request.getRole())
                .status(InvitationStatus.PENDING)
                .token(generateInvitationToken())
                .invitedByUserId(inviterId)
                .message(request.getMessage())
                .expiresAt(LocalDateTime.now().plusHours(appProperties.getInvitation().getExpiryHours()))
                .build();

        invitation = invitationRepository.save(invitation);

        log.info(ProjectMessageConstants.LOG_INVITATION_CREATED_WITH_ID, invitation.getId(), project.getId());

        return mapToResponse(invitation);
    }

    public void acceptInvitation(String token, Long userId) {
        log.info(ProjectMessageConstants.LOG_USER_ACCEPTING_INVITATION_SERVICE, userId, token);

        ProjectInvitation invitation = invitationRepository.findByToken(token)
                .orElseThrow(() -> new ProjectInvitationNotFoundException(ProjectMessageConstants.INVALID_INVITATION_TOKEN));

        validateInvitationForAcceptance(invitation);


        if (memberRepository.existsByProjectIdAndUserId(invitation.getProject().getId(), userId)) {
            throw new DuplicateProjectMemberException(invitation.getProject().getId(), userId);
        }

        by.algin.projectservice.entity.ProjectRole entityRole = roleService.getRoleByNameOrThrow(invitation.getRole().name());

        ProjectMember member = ProjectMember.builder()
                .project(invitation.getProject())
                .userId(userId)
                .role(entityRole)
                .build();

        memberRepository.save(member);

        String acceptedEmail = null;
        try {
            UserResponse acceptingUser = getUserSafely(userId);
            acceptedEmail = acceptingUser.getEmail();
        } catch (UserNotFoundException e) {
            log.warn("User {} not found when accepting invitation, proceeding without email", userId);
        }

        invitation.setStatus(InvitationStatus.ACCEPTED);
        invitation.setAcceptedAt(LocalDateTime.now());
        invitation.setAcceptedByUserId(userId);
        invitation.setAcceptedEmail(acceptedEmail);
        invitationRepository.save(invitation);

        log.info(ProjectMessageConstants.LOG_INVITATION_ACCEPTED_BY_USER, invitation.getId(), userId, acceptedEmail);
    }


    public void cancelInvitation(Long invitationId, Long userId) {
        log.info(ProjectMessageConstants.LOG_USER_CANCELING_INVITATION_SERVICE, userId, invitationId);

        ProjectInvitation invitation = invitationRepository.findById(invitationId)
                .orElseThrow(() -> new ProjectInvitationNotFoundException(invitationId));


        permissionService.requireMemberManagementAccess(invitation.getProject().getId(), userId);

        if (invitation.getStatus() != InvitationStatus.PENDING) {
            throw new IllegalStateException("Can only cancel pending invitations");
        }

        invitation.setStatus(InvitationStatus.CANCELLED);
        invitationRepository.save(invitation);

        log.info(ProjectMessageConstants.LOG_INVITATION_CANCELLED_BY_USER, invitationId, userId);
    }

    @Transactional(readOnly = true)
    public Page<InvitationResponse> getProjectInvitations(Long projectId, Long userId, Pageable pageable) {
        log.debug("Getting invitations for project {} by user {}", projectId, userId);


        permissionService.requireViewAccess(projectId, userId);

        Page<ProjectInvitation> invitations = invitationRepository.findByProjectId(projectId, pageable);
        return invitations.map(this::mapToResponse);
    }

    @Transactional(readOnly = true)
    public InvitationResponse getInvitationByToken(String token) {
        log.debug("Getting invitation by token {}", token);

        ProjectInvitation invitation = invitationRepository.findByToken(token)
                .orElseThrow(() -> new ProjectInvitationNotFoundException(ProjectMessageConstants.INVALID_INVITATION_TOKEN));

        return mapToResponse(invitation);
    }

    @Transactional
    public int cleanupExpiredInvitations() {
        log.info(ProjectMessageConstants.LOG_CLEANING_UP_EXPIRED_INVITATIONS);

        int updatedCount = invitationRepository.updateExpiredInvitations(
                InvitationStatus.PENDING, 
                InvitationStatus.EXPIRED, 
                LocalDateTime.now());

        log.info(ProjectMessageConstants.LOG_MARKED_INVITATIONS_AS_EXPIRED, updatedCount);
        return updatedCount;
    }



    private String generateInvitationToken() {
        return UUID.randomUUID().toString();
    }

    private void validateInvitationForAcceptance(ProjectInvitation invitation) {
        if (invitation.getStatus() != InvitationStatus.PENDING) {
            throw new IllegalStateException("Invitation is not pending");
        }
        if (invitation.getExpiresAt().isBefore(LocalDateTime.now())) {
            throw new IllegalStateException("Invitation has expired");
        }
    }


    private InvitationResponse mapToResponse(ProjectInvitation invitation) {
        UserResponse inviter = null;
        try {
            inviter = getUserSafely(invitation.getInvitedByUserId());
        } catch (UserNotFoundException e) {
            log.warn("Inviter user {} not found for invitation {}, proceeding without inviter details",
                    invitation.getInvitedByUserId(), invitation.getId());
        }
        String invitationUrl = generateInvitationUrl(invitation.getToken());
        return invitation.toResponse(inviter, invitationUrl);
    }

    private UserResponse getUserSafely(Long userId) {
        try {
            return userServiceClient.getUserById(userId);
        } catch (UserNotFoundException e) {
            log.warn("User not found for ID {}: {}", userId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error getting user details for ID {}: {}", userId, e.getMessage(), e);
            throw new RuntimeException("Failed to retrieve user information for invitation", e);
        }
    }

    private String generateInvitationUrl(String token) {
        return "/join?token=" + token;
    }
}
