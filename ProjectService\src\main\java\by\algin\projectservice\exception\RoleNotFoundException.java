package by.algin.projectservice.exception;

import by.algin.common.exception.ResourceNotFoundException;

public class RoleNotFoundException extends ResourceNotFoundException {
    
    private final String roleName;
    
    public RoleNotFoundException(String roleName) {
        super("Role not found: " + roleName);
        this.roleName = roleName;
    }
    
    public RoleNotFoundException(String roleName, String message) {
        super(message);
        this.roleName = roleName;
    }
    
    public RoleNotFoundException(String roleName, String message, Throwable cause) {
        super(message, cause);
        this.roleName = roleName;
    }
    
    public String getRoleName() {
        return roleName;
    }
}
