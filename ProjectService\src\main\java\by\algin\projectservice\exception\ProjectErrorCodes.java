package by.algin.projectservice.exception;

import by.algin.common.exception.ApiErrorCode;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;

@Getter
@RequiredArgsConstructor
public enum ProjectErrorCodes implements ApiErrorCode {

    PROJECT_NOT_FOUND("PROJECT_001", "Project not found", HttpStatus.NOT_FOUND, "PROJECT"),
    PROJECT_ACCESS_DENIED("PROJECT_002", "Access to project denied", HttpStatus.FORBIDDEN, "PROJECT"),
    DUPLICATE_PROJECT_NAME("PROJECT_003", "Project with this name already exists", HttpStatus.CONFLICT, "PROJECT"),

    PROJECT_MEMBER_NOT_FOUND("PROJECT_101", "Project member not found", HttpStatus.NOT_FOUND, "PROJECT_MEMBER"),
    DUPLICATE_PROJECT_MEMBER("PROJECT_102", "User is already a member of this project", HttpStatus.CONFLICT, "PROJECT_MEMBER"),
    CANNOT_REMOVE_PROJECT_OWNER("PROJECT_103", "Cannot remove project owner", HttpStatus.BAD_REQUEST, "PROJECT_MEMBER"),
    CANNOT_CHANGE_OWNER_ROLE("PROJECT_104", "Cannot change project owner role", HttpStatus.BAD_REQUEST, "PROJECT_MEMBER"),

    PROJECT_INVITATION_NOT_FOUND("PROJECT_201", "Project invitation not found", HttpStatus.NOT_FOUND, "PROJECT_INVITATION"),
    INVITATION_EXPIRED("PROJECT_202", "Invitation has expired", HttpStatus.BAD_REQUEST, "PROJECT_INVITATION"),
    INVITATION_ALREADY_ACCEPTED("PROJECT_203", "Invitation has already been accepted", HttpStatus.BAD_REQUEST, "PROJECT_INVITATION"),
    INVITATION_ALREADY_REJECTED("PROJECT_204", "Invitation has already been rejected", HttpStatus.BAD_REQUEST, "PROJECT_INVITATION"),

    BUSINESS_RULE_VIOLATION("PROJECT_301", "Business rule violation", HttpStatus.BAD_REQUEST, "BUSINESS_RULE"),
    INVALID_PROJECT_STATUS_TRANSITION("PROJECT_302", "Invalid project status transition", HttpStatus.BAD_REQUEST, "BUSINESS_RULE"),
    INSUFFICIENT_PERMISSIONS("PROJECT_303", "Insufficient permissions for this operation", HttpStatus.FORBIDDEN, "BUSINESS_RULE"),

    STATUS_TRANSITION_NOT_ALLOWED("PROJECT_401", "Status transition not allowed", HttpStatus.BAD_REQUEST, "STATUS_TRANSITION"),
    INVALID_STATUS_PARAMETER("PROJECT_402", "Invalid status parameter", HttpStatus.BAD_REQUEST, "STATUS_TRANSITION"),
    STATUS_TRANSITION_CONFIG_ERROR("PROJECT_403", "Status transition configuration error", HttpStatus.INTERNAL_SERVER_ERROR, "STATUS_TRANSITION"),

    JWT_TOKEN_MISSING("PROJECT_501", "JWT token is missing", HttpStatus.UNAUTHORIZED, "JWT_AUTH"),
    JWT_TOKEN_INVALID("PROJECT_502", "JWT token is invalid", HttpStatus.UNAUTHORIZED, "JWT_AUTH"),
    JWT_TOKEN_EXPIRED("PROJECT_503", "JWT token has expired", HttpStatus.UNAUTHORIZED, "JWT_AUTH"),
    JWT_TOKEN_MALFORMED("PROJECT_504", "JWT token is malformed", HttpStatus.UNAUTHORIZED, "JWT_AUTH"),
    JWT_TOKEN_UNSUPPORTED("PROJECT_505", "JWT token is unsupported", HttpStatus.UNAUTHORIZED, "JWT_AUTH"),
    JWT_VALIDATION_FAILED("PROJECT_506", "JWT token validation failed", HttpStatus.UNAUTHORIZED, "JWT_AUTH"),
    JWT_REMOTE_VALIDATION_FAILED("PROJECT_507", "Remote JWT token validation failed", HttpStatus.UNAUTHORIZED, "JWT_AUTH"),
    JWT_USER_DATA_INVALID("PROJECT_508", "Invalid user data in JWT token", HttpStatus.UNAUTHORIZED, "JWT_AUTH");

    private final String code;
    private final String defaultMessage;
    private final HttpStatus httpStatus;
    private final String category;
}
