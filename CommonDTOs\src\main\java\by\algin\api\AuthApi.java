package by.algin.api;

import by.algin.dto.request.LoginRequest;
import by.algin.dto.request.RegisterRequest;
import by.algin.dto.request.TokenValidationRequest;
import by.algin.dto.response.ApiResponse;
import by.algin.dto.response.AuthResponse;
import by.algin.dto.response.UserResponse;
import org.springframework.web.bind.annotation.*;

/**
 * Common API interface for Authentication operations
 * This interface should be implemented by UserService and used by Feign clients
 */
@RequestMapping("/api/auth")
public interface AuthApi {

    /**
     * User login
     * @param loginRequest Login credentials
     * @return Authentication response with JWT token
     */
    @PostMapping("/login")
    ApiResponse<AuthResponse> login(@RequestBody LoginRequest loginRequest);

    /**
     * User registration
     * @param registerRequest Registration data
     * @return User response
     */
    @PostMapping("/register")
    ApiResponse<UserResponse> register(@RequestBody RegisterRequest registerRequest);

    /**
     * Validate JWT token
     * @param tokenValidationRequest Token validation request
     * @return Validation result
     */
    @PostMapping("/validate-token")
    ApiResponse<Boolean> validateToken(@RequestBody TokenValidationRequest tokenValidationRequest);

    /**
     * Refresh JWT token
     * @param refreshToken Refresh token
     * @return New authentication response
     */
    @PostMapping("/refresh-token")
    ApiResponse<AuthResponse> refreshToken(@RequestParam("refreshToken") String refreshToken);

    /**
     * Confirm user account
     * @param token Confirmation token
     * @return Confirmation result
     */
    @PostMapping("/confirm")
    ApiResponse<String> confirmAccount(@RequestParam("token") String token);

    /**
     * Resend confirmation email
     * @param email User email
     * @return Operation result
     */
    @PostMapping("/resend-confirmation")
    ApiResponse<String> resendConfirmation(@RequestParam("email") String email);

    /**
     * Get email by token
     * @param token Token
     * @return Email address
     */
    @GetMapping("/email-by-token")
    ApiResponse<String> getEmailByToken(@RequestParam("token") String token);
}
