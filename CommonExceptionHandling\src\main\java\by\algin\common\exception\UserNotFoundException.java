package by.algin.common.exception;

public class UserNotFoundException extends RuntimeException {
    
    public UserNotFoundException() {
        super(CommonErrorCodes.USER_NOT_FOUND.getDefaultMessage());
    }

    public UserNotFoundException(String message) {
        super(message);
    }

    public UserNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
