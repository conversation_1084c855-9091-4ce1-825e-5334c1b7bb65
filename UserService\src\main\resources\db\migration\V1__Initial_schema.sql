CREATE TABLE IF NOT EXISTS roles (
    id BIGSE<PERSON>AL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL UNIQUE
);

CREATE TABLE IF NOT EXISTS users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    enabled BOOLEAN NOT NULL DEFAULT false,
    confirmation_token VARCHAR(255),
    token_creation_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS user_roles (
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    PRIMARY KEY (user_id, role_id),
    FOREIG<PERSON> KEY (user_id) REFERENCES users(id),
    FOREIG<PERSON> KEY (role_id) REFERENCES roles(id)
);
