package by.algin.projectservice.security;

import by.algin.projectservice.exception.JwtAuthenticationException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

@Component
@RequiredArgsConstructor
@Slf4j
public class JwtRequestFilter extends OncePerRequestFilter {

    private final @Lazy JwtAuthenticationService jwtAuthenticationService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
                                  <PERSON><PERSON><PERSON>hai<PERSON> filter<PERSON>hain) throws ServletException, IOException {

        try {
            boolean authenticated = jwtAuthenticationService.processAuthentication(request);

            if (authenticated) {
                log.debug("JWT authentication successful for request: {}", request.getRequestURI());
            } else {
                log.debug("No JWT token found for request: {}", request.getRequestURI());
            }

        } catch (JwtAuthenticationException e) {
            log.warn("JWT authentication failed for request {}: {}",
                request.getRequestURI(), e.getMessage());
            org.springframework.security.core.context.SecurityContextHolder.clearContext();
        } catch (Exception e) {
            log.error("Unexpected error during JWT processing for request {}: {}",
                request.getRequestURI(), e.getMessage(), e);

            org.springframework.security.core.context.SecurityContextHolder.clearContext();
        }
        filterChain.doFilter(request, response);
    }
}

