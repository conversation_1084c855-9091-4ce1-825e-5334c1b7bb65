package by.algin.projectservice.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

@Entity
@Table(name = "project_role_permissions",
       uniqueConstraints = @UniqueConstraint(columnNames = {"role_id", "permission_name"}))
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectRolePermission {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id", nullable = false)
    private ProjectRole role;

    @Column(name = "permission_name", nullable = false, length = 100)
    private String permissionName; 

    @Column(name = "is_granted", nullable = false)
    @Builder.Default
    private Boolean isGranted = true;

    @Column(name = "description", length = 500)
    private String description;

    @Column(name = "created_by", length = 100)
    private String createdBy;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
}
