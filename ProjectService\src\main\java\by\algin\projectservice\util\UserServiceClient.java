package by.algin.projectservice.util;

import by.algin.common.exception.UserNotFoundException;
import by.algin.dto.response.ApiResponse;
import by.algin.dto.response.UserResponse;
import by.algin.projectservice.client.UserServiceFeignClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
public class UserServiceClient {

    private final UserServiceFeignClient userServiceFeignClient;

    public UserResponse getUserById(Long userId) {
        log.debug("Calling UserService to get user by ID: {}", userId);
        try {
            return userServiceFeignClient.getUserById(userId);
        } catch (Exception e) {
            log.error("Failed to get user by ID {} from UserService: {}", userId, e.getMessage());
            throw new UserNotFoundException("User not found with ID: " + userId);
        }
    }

    public List<UserResponse> getUsersByIds(List<Long> userIds) {
        log.debug("Calling UserService to get users by IDs: {}", userIds);
        try {
            ApiResponse<List<UserResponse>> response = userServiceFeignClient.getUsersByIds(userIds);
            return response.getData();
        } catch (Exception e) {
            log.error("Failed to get users by IDs {} from UserService: {}", userIds, e.getMessage());
            throw new RuntimeException("Failed to get users from UserService", e);
        }
    }
 
    public boolean userExists(Long userId) {
        log.debug("Calling UserService to check if user exists: {}", userId);
        try {
            return userServiceFeignClient.userExists(userId);
        } catch (Exception e) {
            log.error("Failed to check user existence for ID {} from UserService: {}", userId, e.getMessage());
            return false;
        }
    }
}
