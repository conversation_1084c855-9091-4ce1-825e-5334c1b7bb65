package by.algin.dto.project;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ProjectRole {
    OWNER("Owner", 4),
    MANAGER("Manager", 3),
    DEVELOPER("Developer", 2),
    VIEWER("Viewer", 1);

    private final String displayName;
    private final int level;

    @JsonValue
    public String toValue() {
        return this.name();
    }

    @JsonCreator
    public static ProjectRole fromValue(String value) {
        return ProjectRole.valueOf(value.toUpperCase());
    }

    /**
     * Check if this role has higher or equal level than the specified role
     * @param other the role to compare with
     * @return true if this role has higher or equal level
     */
    public boolean hasLevelOrHigher(ProjectRole other) {
        return this.level >= other.level;
    }

    /**
     * Check if this role has higher level than the specified role
     * @param other the role to compare with
     * @return true if this role has higher level
     */
    public boolean hasHigherLevel(ProjectRole other) {
        return this.level > other.level;
    }

    /**
     * Check if this role can manage (add/remove/modify) the specified role
     * Generally, a role can manage roles with lower levels
     * @param targetRole the role to check if it can be managed
     * @return true if this role can manage the target role
     */
    public boolean canManage(ProjectRole targetRole) {
        // OWNER can manage all roles
        if (this == OWNER) {
            return true;
        }
        // MANAGER can manage DEVELOPER and VIEWER
        if (this == MANAGER) {
            return targetRole == DEVELOPER || targetRole == VIEWER;
        }
        // DEVELOPER and VIEWER cannot manage other roles
        return false;
    }

    /**
     * Check if this role can view project details
     * @return true if this role can view project details
     */
    public boolean canViewProject() {
        return true; // All roles can view project details
    }

    /**
     * Check if this role can edit project details
     * @return true if this role can edit project details
     */
    public boolean canEditProject() {
        return this == OWNER || this == MANAGER;
    }

    /**
     * Check if this role can delete the project
     * @return true if this role can delete the project
     */
    public boolean canDeleteProject() {
        return this == OWNER;
    }

    /**
     * Check if this role can add members to the project
     * @return true if this role can add members
     */
    public boolean canAddMembers() {
        return this == OWNER || this == MANAGER;
    }

    /**
     * Check if this role can remove members from the project
     * @return true if this role can remove members
     */
    public boolean canRemoveMembers() {
        return this == OWNER || this == MANAGER;
    }
}