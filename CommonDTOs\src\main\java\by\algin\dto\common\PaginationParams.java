package by.algin.dto.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Common pagination parameters for all services
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaginationParams {
    
    /**
     * Page number (0-based)
     */
    private int page = 0;
    
    /**
     * Page size
     */
    private int size = 20;
    
    /**
     * Sort field
     */
    private String sortBy = "createdAt";
    
    /**
     * Sort direction (asc/desc)
     */
    private String sortDir = "desc";

    /**
     * Create pagination params with custom page and size
     */
    public static PaginationParams of(int page, int size) {
        return new PaginationParams(page, size, "createdAt", "desc");
    }

    /**
     * Create pagination params with custom page, size and sorting
     */
    public static PaginationParams of(int page, int size, String sortBy, String sortDir) {
        return new PaginationParams(page, size, sortBy, sortDir);
    }

    /**
     * Create default pagination params
     */
    public static PaginationParams defaultParams() {
        return new PaginationParams();
    }

    /**
     * Create pagination params for specific sort field
     */
    public static PaginationParams withSort(String sortBy, String sortDir) {
        return new PaginationParams(0, 20, sortBy, sortDir);
    }

    /**
     * Validate pagination parameters
     */
    public void validate() {
        if (page < 0) {
            throw new IllegalArgumentException("Page number cannot be negative");
        }
        if (size <= 0) {
            throw new IllegalArgumentException("Page size must be positive");
        }
        if (size > 1000) {
            throw new IllegalArgumentException("Page size cannot exceed 1000");
        }
        if (sortBy == null || sortBy.trim().isEmpty()) {
            sortBy = "createdAt";
        }
        if (sortDir == null || (!sortDir.equalsIgnoreCase("asc") && !sortDir.equalsIgnoreCase("desc"))) {
            sortDir = "desc";
        }
    }

    /**
     * Apply defaults for null values
     */
    public void applyDefaults() {
        if (sortBy == null || sortBy.trim().isEmpty()) {
            sortBy = "createdAt";
        }
        if (sortDir == null || sortDir.trim().isEmpty()) {
            sortDir = "desc";
        }
    }
}
