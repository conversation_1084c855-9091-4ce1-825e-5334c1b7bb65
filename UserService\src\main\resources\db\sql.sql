-- Удалить роли пользователя с id = 1 (предположительно id пользователя 'aboba')
DELETE FROM user_roles
WHERE user_id = (SELECT id FROM users WHERE username = '<EMAIL>');

-- Теперь можно удалить пользователя
DELETE FROM userspublic
WHERE username = '<EMAIL>';

INSERT INTO users (username, email, password, enabled, created_at, updated_at)
VALUES (
           'ADMIN',
           '<EMAIL>',
           '$2a$10$o4v0VroFgfSWMLv9FmpuCOEs0ilufvHTKoJOgfJPOZU0mfpqpnEyO', -- Замените на зашифрованный пароль
           true,
           CURRENT_TIMESTAMP,
           CURRENT_TIMESTAMP
       );

INSERT INTO user_roles (user_id, role_id)
SELECT
    (SELECT id FROM users WHERE username = 'ADMI<PERSON>'),
    (SELECT id FROM roles WHERE name = 'ROLE_ADMIN')
ON CONFLICT (user_id, role_id) DO NOTHING;