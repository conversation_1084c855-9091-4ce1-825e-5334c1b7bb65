package by.algin.projectservice.service;

import by.algin.dto.project.ProjectMemberResponse;
import by.algin.dto.project.ProjectResponse;
import by.algin.dto.response.UserResponse;
import by.algin.projectservice.entity.Project;
import by.algin.projectservice.entity.ProjectMember;
import by.algin.projectservice.util.UserBaseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProjectConversionService {

    private final UserBaseService userBaseService;

    public ProjectResponse convertToProjectResponse(Project project, UserResponse owner) {
        return ProjectResponse.builder()
                .id(project.getId())
                .name(project.getName())
                .description(project.getDescription())
                .status(project.getStatus())
                .ownerId(project.getOwnerId())
                .createdAt(project.getCreatedAt())
                .updatedAt(project.getUpdatedAt())
                .build();
    }

    public List<ProjectResponse> convertProjectsToResponses(List<Project> projects) {
        if (projects.isEmpty()) {
            return List.of();
        }

        Map<Long, UserResponse> owners = getOwnersForProjects(projects);
        
        return projects.stream()
                .map(project -> convertToProjectResponse(project, owners.get(project.getOwnerId())))
                .collect(Collectors.toList());
    }

    public ProjectMemberResponse convertToProjectMemberResponse(ProjectMember member, UserResponse user) {
        by.algin.dto.project.ProjectRole roleDto = 
            by.algin.dto.project.ProjectRole.valueOf(member.getRole().getRoleName());

        return ProjectMemberResponse.builder()
                .userId(member.getUserId())
                .username(user != null ? user.getUsername() : "Unknown User")
                .email(user != null ? user.getEmail() : null)
                .role(roleDto)
                .joinedAt(member.getJoinedAt())
                .build();
    }

    public List<ProjectMemberResponse> convertMembersToResponses(List<ProjectMember> members) {
        if (members.isEmpty()) {
            return List.of();
        }

        Map<Long, UserResponse> users = getUsersForMembers(members);
        
        return members.stream()
                .map(member -> convertToProjectMemberResponse(member, users.get(member.getUserId())))
                .collect(Collectors.toList());
    }

    public Map<Long, UserResponse> getOwnersForProjects(List<Project> projects) {
        List<Long> ownerIds = projects.stream()
                .map(Project::getOwnerId)
                .distinct()
                .collect(Collectors.toList());

        return userBaseService.getUsersByIdsWithFallback(ownerIds);
    }

    public Map<Long, UserResponse> getUsersForMembers(List<ProjectMember> members) {
        List<Long> userIds = members.stream()
                .map(ProjectMember::getUserId)
                .collect(Collectors.toList());

        return userBaseService.getUsersByIdsWithFallback(userIds);
    }
}
