package by.algin.dto.project;

import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProjectSearchRequest {

    @Size(max = 100, message = "Name filter cannot exceed 100 characters")
    private String name;

    private ProjectStatus status;

    private Long ownerId;

    private List<ProjectRole> memberRoles;

    private Long memberId;

    @Builder.Default
    private Integer page = 0;

    @Builder.Default
    private Integer size = 20;

    @Size(max = 50, message = "Sort field cannot exceed 50 characters")
    @Builder.Default
    private String sortBy = "createdAt";

    @Builder.Default
    private String sortDirection = "DESC";
}
