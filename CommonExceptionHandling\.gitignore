﻿# Compiled class files
*.class

# Log files
*.log
*.ctxt

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# Virtual machine crash logs
hs_err_pid*
replay_pid*

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Gradle
.gradle
**/build/
!src/**/build/

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

# Eclipse
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code
.vscode/

# OS Files
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Test files
**/src/test/**/*.class
test-output/
test-results/
coverage/
.coverage
*.lcov
jacoco.exec
jacoco-it.exec

# Surefire reports
surefire-reports/
failsafe-reports/
**/TEST-*.xml

# JUnit Platform
junit-platform*.properties

# Spring Boot
spring.log

# Application properties (keep main ones, ignore environment-specific)
application-local.properties
application-local.yml
application-dev.properties
application-dev.yml
application-prod.properties
application-prod.yml
!application.properties
!application.yml
!application.yaml

# Logs
logs/
*.log.*

# Process files
pids
*.pid
*.seed
*.pid.lock

# Environment variables
.env
.env.test
.env.local
.env.development.local
.env.test.local
.env.production.local

# Spring Boot DevTools
.spring-boot-devtools.properties

# Old backup files
pom-old.xml
